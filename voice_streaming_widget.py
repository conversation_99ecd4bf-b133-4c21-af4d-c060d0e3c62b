#!/usr/bin/env python3
"""
Voice Streaming Widget - Self-contained Desktop Application

A standalone desktop widget that provides voice streaming chatbot functionality
without requiring external dependencies or browser setup.
"""

import sys
import os
import threading
import asyncio
import logging
import json
import tempfile
from pathlib import Path

# Fix SSL certificates BEFORE any other imports
try:
    import certifi
    cert_path = certifi.where()
    os.environ['SSL_CERT_FILE'] = cert_path
    os.environ['REQUESTS_CA_BUNDLE'] = cert_path
    os.environ['CURL_CA_BUNDLE'] = cert_path
except ImportError:
    pass

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# PyQt6 imports
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLabel, QTextEdit, QProgressBar,
                            QSystemTrayIcon, QMenu, QMessageBox, QSplitter)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt, QUrl
from PyQt6.QtGui import QIcon, QFont, QPixmap, QAction
from PyQt6.QtWebEngineWidgets import QWebEngineView

# Widget configuration
from widget_config import get_widget_config, cleanup_widget_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketServerThread(QThread):
    """Thread to run the WebSocket server"""
    
    server_started = pyqtSignal(str, int)  # host, port
    server_error = pyqtSignal(str)
    
    def __init__(self, host="localhost", port=8765):
        super().__init__()
        self.host = host
        self.port = port
        self.server = None
        
    def run(self):
        """Run the WebSocket server in this thread"""
        try:
            # Import here to avoid issues with event loop
            from websocket_voice_server import start_voice_server
            
            logger.info(f"Starting WebSocket server on {self.host}:{self.port}")
            self.server_started.emit(self.host, self.port)
            
            # Start the server (this will block)
            start_voice_server(host=self.host, port=self.port)
            
        except Exception as e:
            logger.error(f"WebSocket server error: {e}")
            self.server_error.emit(str(e))

class VoiceStreamingWidget(QMainWindow):
    """Main widget application window"""

    def __init__(self):
        super().__init__()
        self.websocket_thread = None
        self.server_running = False

        # Load widget configuration
        self.config = get_widget_config()

        # Initialize UI
        self.init_ui()

        # Setup system tray
        if self.config.enable_system_tray:
            self.setup_system_tray()

        # Start WebSocket server if auto-start is enabled
        if self.config.auto_start_server:
            self.start_websocket_server()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle(f"{self.config.page_icon} {self.config.page_title}")
        self.setGeometry(100, 100, self.config.window_width, self.config.window_height)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel(f"{self.config.page_icon} {self.config.page_title}")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Server status
        self.status_label = QLabel("🔴 Server Starting...")
        header_layout.addWidget(self.status_label)
        
        layout.addLayout(header_layout)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Web view for voice interface
        self.web_view = QWebEngineView()
        splitter.addWidget(self.web_view)
        
        # Chat log area
        self.chat_log = QTextEdit()
        self.chat_log.setReadOnly(True)
        self.chat_log.setMaximumHeight(200)
        self.chat_log.setPlaceholderText("Chat conversation will appear here...")
        splitter.addWidget(self.chat_log)
        
        # Set splitter proportions
        splitter.setSizes([500, 200])
        layout.addWidget(splitter)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.minimize_btn = QPushButton("Minimize to Tray")
        self.minimize_btn.clicked.connect(self.hide_to_tray)
        button_layout.addWidget(self.minimize_btn)
        
        button_layout.addStretch()
        
        self.restart_btn = QPushButton("Restart Server")
        self.restart_btn.clicked.connect(self.restart_server)
        self.restart_btn.setEnabled(False)
        button_layout.addWidget(self.restart_btn)
        
        self.quit_btn = QPushButton("Quit")
        self.quit_btn.clicked.connect(self.quit_application)
        button_layout.addWidget(self.quit_btn)
        
        layout.addLayout(button_layout)
        
    def setup_system_tray(self):
        """Setup system tray icon and menu"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return
            
        # Create tray icon
        self.tray_icon = QSystemTrayIcon(self)
        
        # Set icon (you can replace this with a custom icon file)
        self.tray_icon.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_ComputerIcon))
        
        # Create tray menu
        tray_menu = QMenu()
        
        show_action = QAction("Show", self)
        show_action.triggered.connect(self.show_from_tray)
        tray_menu.addAction(show_action)
        
        quit_action = QAction("Quit", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        
        # Show tray icon
        self.tray_icon.show()
        
    def start_websocket_server(self):
        """Start the WebSocket server in a separate thread"""
        if self.websocket_thread and self.websocket_thread.isRunning():
            return

        self.websocket_thread = WebSocketServerThread(
            host=self.config.websocket_host,
            port=self.config.websocket_port
        )
        self.websocket_thread.server_started.connect(self.on_server_started)
        self.websocket_thread.server_error.connect(self.on_server_error)
        self.websocket_thread.start()
        
    def on_server_started(self, host, port):
        """Handle server started signal"""
        self.server_running = True
        self.status_label.setText(f"🟢 Server Running: {host}:{port}")
        self.restart_btn.setEnabled(True)
        
        # Load the voice interface
        self.load_voice_interface(host, port)
        
    def on_server_error(self, error_msg):
        """Handle server error signal"""
        self.server_running = False
        self.status_label.setText(f"🔴 Server Error: {error_msg}")
        self.restart_btn.setEnabled(True)
        
        # Show error message
        QMessageBox.critical(self, "Server Error", f"WebSocket server failed to start:\n{error_msg}")
        
    def load_voice_interface(self, host, port):
        """Load the voice streaming interface in the web view"""
        # Create HTML content with embedded voice interface
        html_content = self.create_widget_html(host, port)
        
        # Load HTML content
        self.web_view.setHtml(html_content)
        
    def create_widget_html(self, host, port):
        """Create HTML content for the widget interface"""
        try:
            # Try to load the existing HTML component and modify it
            html_file = Path("voice_streaming_component.html")
            if html_file.exists():
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()

                # Replace WebSocket URL in the existing HTML
                html_content = html_content.replace(
                    'ws://localhost:8765',
                    f'ws://{host}:{port}'
                )
                return html_content
            else:
                # Fallback to embedded HTML
                return self.create_embedded_html(host, port)

        except Exception as e:
            logger.error(f"Error loading HTML component: {e}")
            return self.create_embedded_html(host, port)

    def create_embedded_html(self, host, port):
        """Create embedded HTML content for the widget"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Voice Streaming Interface</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }}
        .container {{
            max-width: 100%;
            margin: 0 auto;
        }}
        .voice-controls {{
            text-align: center;
            margin-bottom: 20px;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }}
        .voice-button {{
            padding: 12px 25px;
            margin: 8px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}
        .voice-button.start {{
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }}
        .voice-button.stop {{
            background: linear-gradient(45deg, #f44336, #da190b);
            color: white;
        }}
        .voice-button:hover:not(:disabled) {{
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }}
        .voice-button:disabled {{
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
        }}
        .status-indicator {{
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            background: #ccc;
        }}
        .status-indicator.recording {{
            background: #ff4444;
            animation: pulse 1s infinite;
        }}
        .status-indicator.connected {{
            background: #4CAF50;
        }}
        @keyframes pulse {{
            0% {{ opacity: 1; transform: scale(1); }}
            50% {{ opacity: 0.7; transform: scale(1.1); }}
            100% {{ opacity: 1; transform: scale(1); }}
        }}
        .audio-level {{
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 15px 0;
            overflow: hidden;
        }}
        .audio-level-bar {{
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.1s ease;
            border-radius: 4px;
        }}
        .chat-area {{
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            min-height: 250px;
            max-height: 350px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }}
        .message {{
            margin: 8px 0;
            padding: 12px 15px;
            border-radius: 12px;
            max-width: 85%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease;
        }}
        .message.user {{
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.4), rgba(139, 195, 74, 0.4));
            margin-left: auto;
            text-align: right;
            border-bottom-right-radius: 4px;
        }}
        .message.assistant {{
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.4), rgba(30, 136, 229, 0.4));
            margin-right: auto;
            text-align: left;
            border-bottom-left-radius: 4px;
        }}
        @keyframes fadeIn {{
            from {{ opacity: 0; transform: translateY(10px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
        .status-text {{
            margin: 10px 0;
            font-size: 14px;
            font-weight: 500;
        }}
        .welcome-text {{
            text-align: center;
            color: rgba(255,255,255,0.8);
            font-style: italic;
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="voice-controls">
            <div class="status-indicator" id="statusIndicator"></div>
            <button class="voice-button start" id="startBtn" onclick="startStreaming()">🎙️ Start Streaming</button>
            <button class="voice-button stop" id="stopBtn" onclick="stopStreaming()" disabled>⏹️ Stop Streaming</button>
            <div class="status-text" id="statusText">Ready to start</div>
            <div class="audio-level">
                <div class="audio-level-bar" id="audioLevelBar"></div>
            </div>
        </div>

        <div class="chat-area" id="chatArea">
            <div class="welcome-text">
                🎙️ Click "Start Streaming" and speak to begin your conversation...<br>
                💬 Your voice will be transcribed and the AI will respond automatically
            </div>
        </div>
    </div>

    <script>
        let mediaRecorder;
        let audioContext;
        let analyser;
        let microphone;
        let websocket;
        let isStreaming = false;
        let scriptProcessor;
        let isAISpeaking = false;

        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const audioLevelBar = document.getElementById('audioLevelBar');
        const chatArea = document.getElementById('chatArea');

        // Connect to WebSocket server
        function connectWebSocket() {{
            const wsUrl = 'ws://{host}:{port}';
            console.log('Connecting to:', wsUrl);
            websocket = new WebSocket(wsUrl);

            websocket.onopen = function() {{
                console.log('Connected to WebSocket server');
                updateStatus('connected', 'Connected to server - Ready to stream');
            }};

            websocket.onmessage = function(event) {{
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            }};

            websocket.onclose = function() {{
                console.log('WebSocket connection closed');
                updateStatus('disconnected', 'Disconnected from server');
                setTimeout(connectWebSocket, 3000); // Auto-reconnect
            }};

            websocket.onerror = function(error) {{
                console.error('WebSocket error:', error);
                updateStatus('error', 'Connection error - Retrying...');
            }};
        }}

        function handleWebSocketMessage(data) {{
            console.log('Received:', data);

            if (data.status === 'transcribed' && data.text) {{
                addMessage(data.text, 'user');

                if (data.ai_response) {{
                    setTimeout(() => {{
                        addMessage(data.ai_response, 'assistant');

                        if (data.ai_audio) {{
                            playAIResponse(data.ai_audio);
                        }}
                    }}, 500);
                }}
            }} else if (data.status === 'recording') {{
                updateAudioLevel(data.rms || 0);
            }}
        }}

        function addMessage(text, sender) {{
            // Clear welcome text on first message
            if (chatArea.children.length === 1 && chatArea.children[0].className === 'welcome-text') {{
                chatArea.innerHTML = '';
            }}

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${{sender}}`;
            messageDiv.textContent = text;
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }}

        function updateStatus(status, text) {{
            statusText.textContent = text;
            statusIndicator.className = `status-indicator ${{status}}`;
        }}

        function updateAudioLevel(rms) {{
            const percentage = Math.min(rms * 100, 100);
            audioLevelBar.style.width = percentage + '%';
        }}

        async function startStreaming() {{
            try {{
                const stream = await navigator.mediaDevices.getUserMedia({{
                    audio: {{
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }}
                }});

                audioContext = new AudioContext();
                analyser = audioContext.createAnalyser();
                microphone = audioContext.createMediaStreamSource(stream);
                scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);

                analyser.smoothingTimeConstant = 0.8;
                analyser.fftSize = 1024;

                microphone.connect(analyser);
                analyser.connect(scriptProcessor);
                scriptProcessor.connect(audioContext.destination);

                scriptProcessor.onaudioprocess = function(event) {{
                    if (isAISpeaking) return;

                    const inputBuffer = event.inputBuffer;
                    const inputData = inputBuffer.getChannelData(0);

                    const float32Array = new Float32Array(inputData);
                    const arrayBuffer = float32Array.buffer;
                    const uint8Array = new Uint8Array(arrayBuffer);
                    const base64Audio = btoa(String.fromCharCode.apply(null, uint8Array));

                    if (websocket && websocket.readyState === WebSocket.OPEN) {{
                        websocket.send(JSON.stringify({{
                            type: 'audio_chunk',
                            audio_data: base64Audio
                        }}));
                    }}
                }};

                isStreaming = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
                updateStatus('recording', 'Streaming active - Speak naturally');

            }} catch (error) {{
                console.error('Error starting streaming:', error);
                updateStatus('error', 'Microphone access denied');
                alert('Please allow microphone access to use voice streaming.');
            }}
        }}

        function stopStreaming() {{
            if (scriptProcessor) {{
                scriptProcessor.disconnect();
            }}
            if (microphone) {{
                microphone.disconnect();
            }}
            if (audioContext) {{
                audioContext.close();
            }}

            isStreaming = false;
            startBtn.disabled = false;
            stopBtn.disabled = true;
            updateStatus('connected', 'Streaming stopped - Ready to start again');
            updateAudioLevel(0);
        }}

        function playAIResponse(audioBase64) {{
            isAISpeaking = true;

            try {{
                const audioData = atob(audioBase64);
                const arrayBuffer = new ArrayBuffer(audioData.length);
                const uint8Array = new Uint8Array(arrayBuffer);

                for (let i = 0; i < audioData.length; i++) {{
                    uint8Array[i] = audioData.charCodeAt(i);
                }}

                const blob = new Blob([arrayBuffer], {{ type: 'audio/wav' }});
                const audioUrl = URL.createObjectURL(blob);
                const audio = new Audio(audioUrl);

                audio.onended = function() {{
                    isAISpeaking = false;
                    URL.revokeObjectURL(audioUrl);
                }};

                audio.onerror = function() {{
                    isAISpeaking = false;
                    URL.revokeObjectURL(audioUrl);
                }};

                audio.play().catch(error => {{
                    console.error('Error playing audio:', error);
                    isAISpeaking = false;
                }});
            }} catch (error) {{
                console.error('Error processing audio:', error);
                isAISpeaking = false;
            }}
        }}

        // Initialize connection when page loads
        window.onload = function() {{
            updateStatus('connecting', 'Connecting to server...');
            connectWebSocket();
        }};
    </script>
</body>
</html>
        """
        
    def hide_to_tray(self):
        """Hide window to system tray"""
        self.hide()
        if hasattr(self, 'tray_icon'):
            self.tray_icon.showMessage(
                "Voice Streaming Assistant",
                "Application was minimized to tray",
                QSystemTrayIcon.MessageIcon.Information,
                2000
            )
            
    def show_from_tray(self):
        """Show window from system tray"""
        self.show()
        self.raise_()
        self.activateWindow()
        
    def tray_icon_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_from_tray()
            
    def restart_server(self):
        """Restart the WebSocket server"""
        if self.websocket_thread and self.websocket_thread.isRunning():
            self.websocket_thread.terminate()
            self.websocket_thread.wait()
            
        self.server_running = False
        self.status_label.setText("🟡 Restarting Server...")
        self.restart_btn.setEnabled(False)
        
        # Start server again
        QTimer.singleShot(1000, self.start_websocket_server)
        
    def quit_application(self):
        """Quit the application"""
        if self.websocket_thread and self.websocket_thread.isRunning():
            self.websocket_thread.terminate()
            self.websocket_thread.wait()

        # Clean up widget configuration
        cleanup_widget_config()

        QApplication.quit()
        
    def closeEvent(self, event):
        """Handle window close event"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.hide_to_tray()
            event.ignore()
        else:
            self.quit_application()

def main():
    """Main function to start the widget application"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Voice Streaming Assistant")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("ERA-IGNITE")
    
    # Create and show main window
    widget = VoiceStreamingWidget()
    widget.show()
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
