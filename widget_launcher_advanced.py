#!/usr/bin/env python3
"""
Advanced Voice Streaming Widget Launcher

Provides options to launch different versions of the widget and handles
microphone permission issues automatically.
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'PyQt6',
        'websockets',
        'langchain',
        'certifi',
        'dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'dotenv':
                importlib.import_module('dotenv')
            else:
                importlib.import_module(package.lower())
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    return missing_packages

def test_webengine_support():
    """Test if WebEngine with microphone support is available"""
    try:
        from PyQt6.QtWebEngineWidgets import QWebEngineView
        from PyQt6.QtWebEngineCore import QWebEngineSettings
        print("✅ WebEngine available")
        return True
    except ImportError:
        print("❌ WebEngine not available")
        return False

def test_multimedia_support():
    """Test if Qt Multimedia is available for native audio"""
    try:
        from PyQt6.QtMultimedia import QAudioInput, QMediaDevices
        print("✅ Qt Multimedia available")
        return True
    except ImportError:
        print("❌ Qt Multimedia not available")
        return False

def install_dependencies(missing_packages):
    """Install missing dependencies"""
    if not missing_packages:
        return True
        
    print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
    
    try:
        # Try to install from widget requirements
        if os.path.exists('requirements-widget.txt'):
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements-widget.txt'])
        else:
            # Install individual packages
            for package in missing_packages:
                if package == 'dotenv':
                    package = 'python-dotenv'
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        
        print("✅ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment():
    """Setup environment variables and configuration"""
    # Set SSL certificates
    try:
        import certifi
        cert_path = certifi.where()
        os.environ['SSL_CERT_FILE'] = cert_path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['CURL_CA_BUNDLE'] = cert_path
        print("✅ SSL certificates configured")
    except ImportError:
        print("⚠️  SSL certificates not configured (certifi not available)")
    
    # Load .env file if it exists
    if os.path.exists('.env'):
        try:
            from dotenv import load_dotenv
            load_dotenv()
            print("✅ Environment variables loaded from .env")
        except ImportError:
            print("⚠️  Could not load .env file (python-dotenv not available)")
    
    # Ensure config directory exists
    config_dir = Path("config")
    if not config_dir.exists():
        config_dir.mkdir()
        print("✅ Created config directory")

def choose_widget_version():
    """Let user choose which widget version to run"""
    print("\n🎙️ Choose Widget Version:")
    print("=" * 40)
    print("1. WebEngine Version (Recommended)")
    print("   - Uses browser-like interface")
    print("   - Full HTML/JavaScript support")
    print("   - May require microphone permissions")
    print()
    print("2. Native Audio Version")
    print("   - Uses Qt's native audio system")
    print("   - Better microphone compatibility")
    print("   - Simpler interface")
    print()
    print("3. Auto-detect (Recommended)")
    print("   - Automatically choose best version")
    print("   - Falls back if needed")
    print()
    
    while True:
        choice = input("Enter your choice (1-3) or 'q' to quit: ").strip().lower()
        
        if choice == 'q':
            return None
        elif choice == '1':
            return 'webengine'
        elif choice == '2':
            return 'native'
        elif choice == '3':
            return 'auto'
        else:
            print("Invalid choice. Please enter 1, 2, 3, or 'q'")

def auto_detect_best_version():
    """Automatically detect the best widget version to use"""
    webengine_available = test_webengine_support()
    multimedia_available = test_multimedia_support()
    
    if webengine_available:
        print("🔍 Auto-detection: Using WebEngine version (full features)")
        return 'webengine'
    elif multimedia_available:
        print("🔍 Auto-detection: Using Native Audio version (better compatibility)")
        return 'native'
    else:
        print("❌ Auto-detection: No suitable audio system found")
        return None

def launch_widget(version):
    """Launch the specified widget version"""
    try:
        if version == 'webengine':
            print("🚀 Starting WebEngine Widget...")
            from voice_streaming_widget import main as widget_main
            widget_main()
        elif version == 'native':
            print("🚀 Starting Native Audio Widget...")
            from voice_streaming_widget_native import main as native_main
            native_main()
        else:
            print("❌ Unknown widget version")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import widget: {e}")
        return False
    except Exception as e:
        print(f"❌ Error starting widget: {e}")
        return False

def main():
    """Main launcher function"""
    print("🎙️ Advanced Voice Streaming Widget Launcher")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        sys.exit(1)
    
    print("\n📋 Checking dependencies...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        response = input("Would you like to install them automatically? (y/n): ")
        
        if response.lower() in ['y', 'yes']:
            if not install_dependencies(missing_packages):
                input("Press Enter to exit...")
                sys.exit(1)
        else:
            print("❌ Cannot start widget without required dependencies")
            input("Press Enter to exit...")
            sys.exit(1)
    
    print("\n⚙️  Setting up environment...")
    setup_environment()
    
    print("\n🔍 Checking audio system support...")
    webengine_ok = test_webengine_support()
    multimedia_ok = test_multimedia_support()
    
    if not webengine_ok and not multimedia_ok:
        print("❌ No suitable audio system found!")
        print("Please install PyQt6-WebEngine or ensure Qt Multimedia is available.")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Choose widget version
    version = choose_widget_version()
    
    if version is None:
        print("👋 Goodbye!")
        sys.exit(0)
    elif version == 'auto':
        version = auto_detect_best_version()
        if version is None:
            input("Press Enter to exit...")
            sys.exit(1)
    
    print("\n🚀 Starting Voice Streaming Widget...")
    print("=" * 50)
    
    success = launch_widget(version)
    
    if not success:
        print("\n❌ Failed to start widget")
        
        # Offer fallback option
        if version == 'webengine' and multimedia_ok:
            print("🔄 Trying fallback to Native Audio version...")
            success = launch_widget('native')
        elif version == 'native' and webengine_ok:
            print("🔄 Trying fallback to WebEngine version...")
            success = launch_widget('webengine')
        
        if not success:
            input("Press Enter to exit...")
            sys.exit(1)

if __name__ == "__main__":
    # Add current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    main()
