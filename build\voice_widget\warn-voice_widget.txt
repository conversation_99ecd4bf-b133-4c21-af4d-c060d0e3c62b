
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named urllib.urlencode - imported by urllib (conditional), requests_toolbelt._compat (conditional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed, optional), psutil (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), langchain_community.utilities.pebblo (delayed, optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named 'collections.abc' - imported by logging (top-level), inspect (top-level), typing (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), traceback (top-level), asyncio.base_events (top-level), http.client (top-level), asyncio.coroutines (top-level), configparser (top-level), numpy.lib._npyio_impl (top-level), numpy.lib._function_base_impl (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy._typing._array_like (top-level), yaml.constructor (top-level), numpy.random.bit_generator (top-level), typing_extensions (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), PIL.Image (top-level), PIL._typing (top-level), xml.etree.ElementTree (top-level), PIL.ImageFilter (top-level), PIL.ImagePalette (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.PngImagePlugin (top-level), pandas._typing (top-level), pytz.lazy (optional), pandas.util._exceptions (conditional), pandas._config.config (conditional), pandas.util.version (top-level), pandas.core.dtypes.inference (conditional), setuptools (top-level), setuptools._distutils.filelist (top-level), setuptools._distutils.util (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils._modified (top-level), setuptools._distutils.compat (top-level), setuptools._distutils.spawn (top-level), setuptools._distutils.compilers.C.base (top-level), setuptools._distutils.fancy_getopt (top-level), setuptools._reqs (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), importlib_metadata (top-level), importlib_metadata._meta (top-level), setuptools._distutils.command.bdist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), setuptools._distutils.extension (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), pkg_resources (top-level), setuptools._vendor.platformdirs.windows (conditional), setuptools.command.egg_info (top-level), setuptools._distutils.command.build (top-level), setuptools._distutils.command.sdist (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), wheel.cli.convert (top-level), wheel.cli.tags (top-level), setuptools._distutils.command.build_ext (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), setuptools._distutils.compilers.C.msvc (top-level), scipy._lib._array_api (top-level), scipy._lib.array_api_compat.common._helpers (top-level), scipy._lib.array_api_compat.common._typing (top-level), scipy._lib.array_api_compat._internal (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib._docscrape (top-level), scipy._lib.array_api_extra._delegation (top-level), scipy._lib.array_api_extra._lib._funcs (top-level), scipy._lib.array_api_extra._lib._at (top-level), scipy._lib.array_api_extra._lib._utils._helpers (top-level), scipy._lib.array_api_extra._lib._lazy (top-level), scipy._lib.array_api_extra.testing (top-level), scipy.special._support_alternative_backends (top-level), scipy._lib.pyprima (top-level), scipy.stats._stats_py (top-level), scipy.spatial.distance (top-level), scipy._lib.doccer (top-level), scipy.integrate._quadrature (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._resampling (top-level), scipy.stats._multicomp (top-level), scipy.stats._qmc (top-level), scipy.stats._sensitivity_analysis (top-level), scipy.ndimage._filters (top-level), scipy.ndimage._ni_support (top-level), scipy.linalg._decomp_cossin (top-level), pandas.util._validators (top-level), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.util._decorators (conditional), pandas.core.frame (top-level), pandas.core.dtypes.concat (conditional), pandas.core.sorting (conditional), pandas.core.indexes.category (conditional), requests.compat (top-level), OpenSSL.SSL (top-level), cryptography.utils (top-level), cryptography.x509.name (top-level), cryptography.x509.base (top-level), cryptography.hazmat.bindings.openssl.binding (top-level), cryptography.x509.extensions (top-level), OpenSSL.crypto (top-level), pandas.core.series (top-level), pandas.core.base (conditional), pandas.core.apply (conditional), pandas.core.groupby.base (conditional), pandas.core.indexing (conditional), pandas.core.internals.blocks (conditional), pandas.core.arrays.masked (conditional), pandas.core.arrays.numeric (conditional), pandas.core.arrays.timedeltas (conditional), pandas.io.formats.format (top-level), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (conditional), pandas.core.indexes.datetimelike (conditional), pandas.core.reshape.concat (conditional), pandas.io.common (top-level), pandas.io.formats.printing (top-level), pandas.core.indexes.multi (top-level), pandas.io.formats.html (conditional), pandas.io.formats.string (conditional), pandas.io.formats.csvs (top-level), pandas.core.arrays.interval (conditional), pandas.core.indexes.interval (conditional), pandas.core.arrays.period (conditional), pandas.core.indexes.period (conditional), pandas.core.tools.datetimes (conditional), pandas.core.internals.managers (top-level), pandas.core.internals.ops (conditional), pandas.core.interchange.dataframe_protocol (conditional), pandas.core.groupby.groupby (top-level), pandas.core.strings.base (conditional), pandas.core.strings.object_array (conditional), pyarrow.vendored.docscrape (top-level), pandas.core.arrays.string_arrow (conditional), pandas.core.groupby.indexing (top-level), pandas.core.window.rolling (conditional), pandas.core.resample (conditional), pandas.io._util (conditional), pandas.io.json._normalize (conditional), pandas.io.parsers.base_parser (conditional), pandas.io.parsers.c_parser_wrapper (conditional), pandas.io.parsers.python_parser (top-level), pandas.io.parsers.readers (conditional), pandas.io.json._json (conditional), pandas.io.stata (conditional), pandas.core.internals.array_manager (conditional), pandas.core.internals.construction (conditional), pandas.core.methods.describe (conditional), pandas.core.generic (conditional), pandas.core.computation.parsing (conditional), pandas.io.formats.excel (top-level), pandas.io.formats.css (conditional), pandas.io.excel._base (top-level), pandas.io.excel._util (top-level), pandas.compat.pickle_compat (conditional), pandas.core.computation.ops (conditional), pandas.core.computation.align (conditional), pandas.io.pytables (conditional), pandas.io.sql (conditional), sqlalchemy.util.compat (conditional), sqlalchemy.util.typing (top-level), sqlalchemy.sql.coercions (top-level), sqlalchemy.sql.traversals (top-level), sqlalchemy.sql.compiler (top-level), sqlalchemy.sql.dml (top-level), sqlalchemy.sql.sqltypes (top-level), sqlalchemy.engine.row (top-level), sqlalchemy.sql.lambdas (top-level), sqlalchemy.engine.url (top-level), sqlalchemy.orm.query (top-level), sqlite3.dbapi2 (top-level), pandas.io.formats.style_render (top-level), markupsafe (top-level), pandas.io.formats.style (conditional), pandas.core.groupby.grouper (conditional), pandas.core.groupby.ops (conditional), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.groupby.generic (conditional), pandas.core.reshape.merge (top-level), pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (conditional), pandas.io.formats.info (conditional), fsspec.dircache (top-level), fsspec.mapping (top-level), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.datetimelike (conditional), pandas.core.arrays.datetimes (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.arrays._mixins (conditional), pandas.core.arrays.categorical (conditional), pandas.core.reshape.melt (conditional), pandas.core.interchange.dataframe (conditional), pandas.io.feather_format (conditional), pyarrow.pandas_compat (top-level), pandas.io.xml (conditional), pandas.core.reshape.pivot (top-level), pandas.core.arrays.base (conditional), pandas.core.internals.concat (conditional), pandas.core.indexes.base (conditional), pandas.core.arrays.numpy_ (conditional), pandas.core.dtypes.cast (conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (conditional), pandas.core.util.hashing (conditional), pandas.core.reshape.encoding (top-level), pandas._config.localization (conditional), pandas._testing.contexts (conditional), pandas._testing._warnings (conditional), pandas.io.html (conditional), pandas.io.sas.sasreader (conditional), pandas.io.spss (conditional), pyautogui (conditional), PIL.ImageDraw (top-level), langchain_core._api.beta_decorator (top-level), langchain_core._api.deprecation (top-level), langchain_core.utils.aiter (top-level), langchain_core.utils.formatting (top-level), langchain_core.utils.iter (top-level), pydantic_core._pydantic_core (top-level), pydantic_core.core_schema (top-level), pydantic._internal._generate_schema (top-level), pydantic._internal._namespace_utils (top-level), pydantic._internal._typing_extra (top-level), pydantic.v1.typing (top-level), pydantic.v1.fields (top-level), pydantic.v1.validators (top-level), pydantic._internal._serializers (top-level), pydantic._internal._std_types_schema (top-level), langchain_core.utils.utils (top-level), langchain_core.callbacks.base (conditional), tornado.gen (top-level), langchain_core.agents (top-level), langchain_core.messages.base (conditional), langchain_core.prompts.base (top-level), langchain_core.language_models.base (top-level), langchain_core.caches (top-level), langchain_core.runnables.base (top-level), langchain_core.runnables.config (top-level), langchain_core.runnables.utils (top-level), langchain_core.runnables.schema (conditional), langsmith.client (top-level), requests_toolbelt._compat (conditional), langsmith.utils (top-level), httpx._models (top-level), anyio._core._eventloop (top-level), anyio.abc._eventloop (top-level), anyio._core._exceptions (top-level), anyio._core._tasks (top-level), anyio.abc._tasks (top-level), anyio._core._testing (top-level), anyio.from_thread (top-level), anyio.abc._sockets (top-level), anyio._core._typedattr (top-level), anyio.abc._streams (top-level), anyio._core._sockets (top-level), anyio.to_thread (top-level), anyio.streams.stapled (top-level), anyio.streams.tls (top-level), anyio.abc._testing (top-level), anyio._core._fileio (top-level), anyio._core._signals (top-level), anyio._core._subprocesses (top-level), click.core (top-level), click.types (top-level), click._compat (top-level), click._winconsole (top-level), click.exceptions (top-level), click.utils (top-level), click.shell_completion (top-level), click.formatting (top-level), click.parser (top-level), click._textwrap (top-level), click.termui (top-level), click._termui_impl (top-level), langsmith.run_helpers (top-level), langsmith.run_trees (top-level), langchain_core.tracers.core (conditional), langchain_core.documents.base (conditional), langchain_core.documents.compressor (conditional), langchain_core.documents.transformers (conditional), langchain_core.tracers.base (conditional), langsmith.evaluation._arunner (top-level), langsmith._internal._aiter (top-level), langsmith.evaluation._runner (top-level), langsmith.evaluation.evaluator (top-level), langchain_core.utils.json_schema (conditional), langchain_core.tools.simple (top-level), langchain_core.tools.base (conditional), langchain_core.tools.structured (top-level), langchain.agents.agent (top-level), langchain_core.prompt_values (top-level), langchain_core.utils.mustache (top-level), langchain_core.vectorstores.base (conditional), langchain_core.vectorstores.in_memory (conditional), langchain_core.indexing.api (top-level), langchain_core.document_loaders.base (conditional), langchain_core.document_loaders.blob_loaders (conditional), langchain_core.document_loaders.langsmith (top-level), langchain_core.indexing.base (conditional), langchain_core.prompts.chat (conditional), langchain.agents.agent_iterator (top-level), langchain_core.chat_history (conditional), langchain_core.stores (top-level), multidict._abc (top-level), multidict._multidict_py (top-level), multidict (conditional), attr._compat (top-level), attr._make (top-level), yarl._query (top-level), yarl._url (top-level), propcache._helpers_py (top-level), yarl._path (top-level), aiohttp.web (top-level), aiohttp.abc (top-level), frozenlist (top-level), aiohttp.compression_utils (conditional), aiohttp.payload (conditional), aiohttp.connector (conditional), aiohttp.web_response (top-level), aiohttp.client_middlewares (top-level), langchain_core.language_models.llms (top-level), playwright._impl._connection (top-level), playwright._impl._js_handle (top-level), playwright._impl._set_input_files_helpers (top-level), playwright._impl._assertions (top-level), google.protobuf.internal.containers (top-level), google.protobuf.internal.well_known_types (top-level), langchain_core.utils.html (top-level), langchain_core.language_models.chat_models (top-level), langchain_core.language_models._utils (top-level), langchain_core.tracers._streaming (top-level), langchain_core.output_parsers.transform (conditional), langchain_core.chat_sessions (top-level), langchain.chains.openai_functions.base (top-level), jsonpatch (optional), jsonpointer (top-level), langchain.chains.structured_output.base (top-level), langchain.chains.openai_functions.citation_fuzzy_match (top-level), langchain.chains.llm (top-level), langchain_core.runnables.configurable (top-level), langchain_core.runnables.graph (conditional), langchain_core.runnables.graph_ascii (top-level), huggingface_hub.keras_mixin (top-level), langchain.chains.question_answering.chain (top-level), langchain.chains.combine_documents.map_rerank (top-level), langchain.chains.qa_with_sources.loading (top-level), langchain.agents.format_scratchpad.openai_functions (top-level), langchain.agents.format_scratchpad.tools (top-level), langchain.agents.output_parsers.self_ask (top-level), langchain.memory.entity (top-level), langchain.memory.vectorstore (top-level), blinker.base (top-level), blinker._utilities (top-level), streamlit.type_util (top-level), streamlit.delta_generator (top-level), streamlit.runtime.state.query_params_proxy (top-level), streamlit.runtime.metrics_util (top-level), streamlit.runtime.uploaded_file_manager (conditional), streamlit.commands.navigation (top-level), streamlit.runtime.state.session_state_proxy (top-level), streamlit.elements.lib.utils (conditional), streamlit.runtime.state.safe_session_state (conditional), streamlit.runtime.state.query_params (top-level), streamlit.runtime.state.session_state (top-level), streamlit.dataframe_util (top-level), streamlit.elements.spinner (conditional), streamlit.runtime.caching.cached_message_replay (conditional), streamlit.runtime.caching.hashing (top-level), cachetools (top-level), streamlit.runtime.secrets (top-level), watchdog.utils.patterns (conditional), watchdog.events (conditional), watchdog.observers.inotify_c (conditional), watchdog.utils.dirsnapshot (conditional), watchdog.observers.kqueue (conditional), watchdog.observers.polling (conditional), streamlit.runtime.memory_session_storage (conditional), streamlit.runtime.runtime (conditional), streamlit.elements.lib.column_config_utils (top-level), streamlit.elements.lib.column_types (conditional), streamlit.elements.lib.dicttools (conditional), streamlit.elements.lib.pandas_styler_utils (top-level), streamlit.elements.lib.policies (conditional), streamlit.elements.arrow (conditional), streamlit.elements.lib.color_util (top-level), streamlit.elements.lib.built_in_chart_utils (conditional), altair.utils.core (top-level), referencing._core (top-level), referencing.typing (top-level), referencing.jsonschema (top-level), jsonschema._utils (top-level), jsonschema.exceptions (conditional), jsonschema._types (conditional), jsonschema.validators (top-level), jsonschema._typing (top-level), jsonschema.protocols (conditional), toolz.itertoolz (top-level), toolz.dicttoolz (top-level), streamlit.elements.deck_gl_json_chart (conditional), streamlit.elements.lib.image_utils (top-level), streamlit.elements.layouts (top-level), streamlit.elements.map (conditional), streamlit.elements.plotly_chart (conditional), streamlit.elements.vega_charts (conditional), streamlit.elements.lib.file_uploader_utils (conditional), streamlit.elements.widgets.file_uploader (conditional), streamlit.elements.widgets.button_group (top-level), streamlit.elements.lib.options_selector_utils (conditional), streamlit.elements.widgets.chat (top-level), streamlit.elements.widgets.data_editor (conditional), streamlit.elements.widgets.multiselect (top-level), streamlit.elements.widgets.radio (conditional), streamlit.elements.widgets.select_slider (conditional), streamlit.elements.widgets.selectbox (conditional), streamlit.elements.widgets.slider (top-level), streamlit.elements.widgets.time_widgets (top-level), streamlit.elements.write (top-level), streamlit.connections.util (conditional), streamlit.connections.snowpark_connection (conditional), streamlit.runtime.context (top-level), tornado.httputil (top-level), streamlit.user_info (top-level), streamlit.auth_util (top-level), streamlit.commands.echo (conditional), streamlit.commands.page_config (top-level), langchain.agents.openai_functions_agent.base (top-level), langchain.agents.mrkl.base (top-level), langchain.agents.utils (top-level), langchain.agents.conversational_chat.base (top-level), langchain.agents.conversational.base (top-level), langchain.agents.initialize (top-level), langchain.agents.chat.base (top-level), langchain.agents.openai_functions_multi_agent.base (top-level), langchain.agents.react.base (top-level), langchain.agents.self_ask_with_search.base (top-level), langchain.agents.structured_chat.base (top-level), langchain.chains.api.base (top-level), langchain.agents.json_chat.base (top-level), langchain.agents.openai_tools.base (top-level), langchain.agents.react.agent (top-level), langchain.agents.tool_calling_agent.base (top-level), langchain.agents.xml.base (top-level), sqlalchemy.dialects.postgresql.psycopg2 (top-level), faiss.extra_wrappers (top-level), langchain.chains.query_constructor.base (top-level), langchain_core.structured_query (conditional), langchain.chains.query_constructor.parser (top-level), langchain.retrievers.document_compressors.base (top-level), langchain.retrievers.document_compressors.chain_extract (top-level), langchain.retrievers.document_compressors.chain_filter (top-level), langchain.retrievers.document_compressors.cohere_rerank (top-level), langchain.retrievers.document_compressors.cross_encoder_rerank (top-level), langchain.retrievers.document_compressors.embeddings_filter (top-level), langchain.retrievers.document_compressors.listwise_rerank (top-level), langchain.retrievers.ensemble (top-level), langchain.retrievers.multi_query (top-level), langchain.storage.encoder_backed (top-level), langchain.storage.file_system (top-level), langchain.retrievers.parent_document_retriever (top-level), langchain.retrievers.self_query.base (top-level), langchain.evaluation.agents.trajectory_eval_chain (top-level), langchain.evaluation.schema (top-level), langchain.evaluation.criteria.eval_chain (top-level), langchain_openai.chat_models.azure (top-level), langchain_openai.chat_models.base (top-level), langchain_openai.embeddings.azure (top-level), langchain_openai.embeddings.base (top-level), langchain_openai.llms.azure (top-level), langchain_openai.llms.base (top-level), langchain.evaluation.loading (top-level), langchain.evaluation.qa.eval_chain (top-level), langchain_core.tracers.context (conditional), langchain_core.tracers.evaluation (conditional), langchain_core.tracers.memory_stream (top-level), langchain_core.tracers.log_stream (conditional), langsmith._internal._operations (top-level), langsmith._internal._multipart (top-level), langchain.smith.evaluation.config (top-level), langchain.smith.evaluation.progress (top-level), langchain_core.prompts.structured (top-level), langsmith._internal._embedding_distance (top-level), langsmith.async_client (top-level), langsmith.testing._internal (top-level), langchain_core.callbacks.manager (conditional), langchain_core.runnables.fallbacks (top-level), langchain_core.tracers.root_listeners (top-level), langchain_core.runnables.passthrough (top-level), langchain_core.tracers.event_stream (conditional), langchain_core.beta.runnables.context (top-level), langchain_core.runnables.branch (top-level), langchain_core.runnables.history (top-level), langchain_core.runnables.router (top-level), langchain_core.language_models.fake (top-level), langchain_core.language_models.fake_chat_models (top-level), langchain_core.output_parsers.list (conditional), langchain_core.output_parsers.xml (top-level), langchain_core.messages.utils (top-level), langchain_core.callbacks.usage (top-level), litellm.litellm_core_utils.streaming_handler (top-level), langchain_community.chat_models.outlines (top-level), langchain_community.chat_models.zhipuai (top-level), anyio._backends._asyncio (top-level), anyio._core._asyncio_selector_thread (top-level), anyio._backends._trio (top-level), pydantic.experimental.pipeline (top-level), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level), scipy._lib.array_api_compat.common._fft (top-level), scipy.constants._codata (top-level), sqlalchemy.ext.baked (top-level)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), _pyrepl.unix_console (delayed, optional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pyimod02_importers - imported by C:\Users\<USER>\miniconda3\envs\cua\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\miniconda3\envs\cua\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), psutil._compat (delayed, optional), _pyrepl.unix_console (top-level), tqdm.utils (delayed, optional), filelock._unix (conditional, optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by numpy.random.bit_generator (top-level), setuptools._distutils.dist (conditional), pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), scipy._lib.array_api_compat.common._typing (conditional), pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), httpx._transports.wsgi (conditional), git.objects.fun (conditional), streamlit.runtime.state.query_params (conditional), streamlit.runtime.state.query_params_proxy (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), _pyrepl.pager (delayed, optional), getpass (optional), psutil._compat (delayed, optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level), tqdm.utils (delayed, optional), click._termui_impl (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Pipe - imported by multiprocessing (top-level), uvicorn.supervisors.multiprocess (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), langsmith._internal._background_thread (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), rlcompleter (optional), pdb (delayed, optional), site (delayed, optional), websockets.__main__ (optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional), jsonschema_specifications._core (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named Quartz - imported by pygetwindow._pygetwindow_macos (top-level), pyautogui._pyautogui_osx (optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional), pyautogui._pyautogui_osx (top-level)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional)
missing module named PyQt5 - imported by pyperclip (delayed, conditional, optional)
missing module named 'qtpy.QtWidgets' - imported by pyperclip (delayed, optional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic.deprecated.copy_internals (delayed, conditional), langchain_core.utils.pydantic (top-level), langchain_core.load.dump (top-level), langchain_core.load.serializable (top-level), langchain_core.prompts.base (top-level), langchain_core.language_models.base (top-level), langchain_core.outputs.chat_result (top-level), langchain_core.outputs.llm_result (top-level), langchain_core.outputs.run_info (top-level), langchain_core.runnables.base (top-level), langsmith.schemas (optional), langchain_core.documents.compressor (top-level), langsmith.evaluation.evaluator (optional), langchain_core.tools.base (top-level), langchain_core.utils.function_calling (top-level), langchain_core.tools.convert (top-level), langchain_core.tools.retriever (top-level), langchain_core.prompts.few_shot (top-level), langchain_core.example_selectors.length_based (top-level), langchain_core.prompts.prompt (top-level), langchain_core.prompts.string (top-level), langchain_core.example_selectors.semantic_similarity (top-level), langchain_core.embeddings.fake (top-level), langchain.agents.agent (top-level), langchain_core.chat_history (top-level), langchain_community.utilities.alpha_vantage (top-level), langchain_community.utilities.apify (top-level), langchain_community.document_loaders.apify_dataset (top-level), langchain_community.utilities.arxiv (top-level), langchain_community.utilities.bibtex (top-level), openai.resources.beta.realtime.realtime (top-level), langchain_community.utilities.brave_search (top-level), langchain_community.document_loaders.docugami (top-level), langchain_community.document_loaders.dropbox (top-level), langchain_community.document_loaders.github (top-level), langchain_community.document_loaders.googledrive (top-level), langchain_community.document_loaders.onedrive_file (top-level), langchain_community.utilities.pebblo (top-level), langchain_community.utilities.pubmed (top-level), langchain_community.document_loaders.base_o365 (top-level), pydantic_settings.sources (top-level), langchain_community.utilities.tensorflow_datasets (top-level), langchain_community.utilities.openweathermap (top-level), langchain_community.utilities.wikipedia (top-level), langchain_community.utilities.arcee (top-level), langchain_community.utilities.asknews (top-level), langchain_community.utilities.awslambda (top-level), langchain_community.utilities.bing_search (top-level), langchain_community.utilities.dataherald (top-level), langchain_community.utilities.duckduckgo_search (top-level), langchain_community.utilities.golden_query (top-level), langchain_community.utilities.google_books (top-level), langchain_community.utilities.google_finance (top-level), langchain_community.utilities.google_jobs (top-level), langchain_community.utilities.google_lens (top-level), langchain_community.utilities.google_places_api (top-level), langchain_community.utilities.google_scholar (top-level), langchain_community.utilities.google_search (top-level), langchain_community.utilities.google_serper (top-level), langchain_community.utilities.google_trends (top-level), langchain_community.utilities.graphql (top-level), langchain_community.utilities.infobip (top-level), langchain_community.utilities.jira (top-level), langchain_community.utilities.merriam_webster (top-level), langchain_community.utilities.metaphor_search (top-level), langchain_community.utilities.mojeek_search (top-level), langchain_community.utilities.nasa (top-level), langchain_community.utilities.nvidia_riva (top-level), langchain_community.utilities.outline (top-level), langchain_community.utilities.passio_nutrition_ai (top-level), langchain_community.utilities.powerbi (top-level), langchain_community.utilities.rememberizer (top-level), langchain_community.utilities.requests (top-level), langchain_community.utilities.scenexplain (top-level), langchain_community.utilities.searchapi (top-level), langchain_community.utilities.searx_search (top-level), langchain_community.utilities.serpapi (top-level), langchain_community.utilities.stackexchange (top-level), langchain_community.utilities.steam (top-level), langchain_community.tools.ainetwork.app (top-level), langchain_community.tools.ainetwork.owner (top-level), langchain_community.tools.ainetwork.rule (top-level), langchain_community.tools.ainetwork.transfer (top-level), langchain_community.tools.ainetwork.value (top-level), langchain_community.tools.arxiv.tool (top-level), langchain_community.tools.asknews.tool (top-level), langchain_community.tools.bearly.tool (top-level), langchain_community.tools.cassandra_database.tool (top-level), langchain_community.utilities.cassandra_database (top-level), langchain_community.tools.connery.service (top-level), langchain_community.tools.connery.models (top-level), langchain_community.tools.connery.tool (top-level), langchain_community.tools.dataherald.tool (top-level), langchain_community.tools.ddg_search.tool (top-level), langchain_community.tools.e2b_data_analysis.tool (top-level), langchain_community.tools.edenai.audio_speech_to_text (top-level), langchain_community.tools.edenai.audio_text_to_speech (top-level), langchain_community.tools.edenai.image_explicitcontent (top-level), langchain_community.tools.edenai.image_objectdetection (top-level), langchain_community.tools.edenai.ocr_identityparser (top-level), langchain_community.tools.edenai.ocr_invoiceparser (top-level), langchain_community.tools.edenai.text_moderation (top-level), langchain_community.tools.file_management.copy (top-level), langchain_community.tools.file_management.utils (top-level), langchain_community.tools.file_management.delete (top-level), langchain_community.tools.file_management.file_search (top-level), langchain_community.tools.file_management.list_dir (top-level), langchain_community.tools.file_management.move (top-level), langchain_community.tools.file_management.read (top-level), langchain_community.tools.file_management.write (top-level), langchain_community.tools.financial_datasets.balance_sheets (top-level), langchain_community.utilities.financial_datasets (top-level), langchain_community.tools.financial_datasets.cash_flow_statements (top-level), langchain_community.tools.financial_datasets.income_statements (top-level), langchain_community.tools.gmail.create_draft (top-level), langchain_community.tools.gmail.get_message (top-level), langchain_community.tools.gmail.get_thread (top-level), langchain_community.tools.gmail.search (top-level), langchain_community.tools.gmail.send_message (top-level), langchain_community.tools.google_books (top-level), langchain_community.tools.google_places.tool (top-level), langchain_community.tools.jina_search.tool (top-level), langchain_community.utilities.jina_search (top-level), langchain_community.tools.json.tool (top-level), langchain_community.tools.office365.create_draft_message (top-level), langchain_community.tools.office365.events_search (top-level), langchain_community.tools.office365.messages_search (top-level), langchain_community.tools.office365.send_event (top-level), langchain_community.tools.office365.send_message (top-level), langchain_community.tools.openapi.utils.api_models (top-level), langchain_community.tools.playwright.click (top-level), langchain_community.tools.playwright.current_page (top-level), langchain_community.tools.playwright.extract_hyperlinks (top-level), langchain_community.tools.playwright.extract_text (top-level), langchain_community.tools.playwright.get_elements (top-level), langchain_community.tools.playwright.navigate (top-level), langchain_community.tools.playwright.navigate_back (top-level), langchain_community.tools.plugin (top-level), langchain_community.tools.polygon.aggregates (top-level), langchain_community.utilities.polygon (top-level), langchain_community.tools.polygon.financials (top-level), langchain_community.tools.polygon.last_quote (top-level), langchain_community.tools.polygon.ticker_news (top-level), langchain_core.language_models.chat_models (top-level), langchain_community.chat_models.openai (top-level), langchain_community.adapters.openai (top-level), langchain_core.output_parsers.openai_functions (top-level), langchain.chains.openai_functions.base (top-level), langchain.chains.structured_output.base (top-level), langchain.chains.openai_functions.citation_fuzzy_match (top-level), langchain_core.runnables.configurable (top-level), langchain_core.runnables.graph (conditional), langchain.chains.base (top-level), langchain.chains.openai_functions.extraction (top-level), langchain.chains.openai_functions.qa_with_structure (top-level), langchain_community.tools.reddit_search.tool (top-level), langchain_community.utilities.reddit_search (top-level), langchain_community.tools.requests.tool (top-level), langchain_community.tools.scenexplain.tool (top-level), langchain_community.tools.searx_search.tool (top-level), langchain_community.tools.shell.tool (top-level), langchain_community.tools.slack.get_message (top-level), langchain_community.tools.slack.schedule_message (top-level), langchain_community.tools.slack.send_message (top-level), langchain_community.tools.sleep.tool (top-level), langchain_community.tools.spark_sql.tool (top-level), langchain_community.tools.sql_database.tool (top-level), langchain_community.tools.tavily_search.tool (top-level), langchain_community.utilities.tavily_search (top-level), langchain_community.tools.vectorstore.tool (top-level), langchain_community.llms.ai21 (top-level), langchain_community.llms.azureml_endpoint (top-level), langchain_community.llms.bedrock (top-level), langchain_community.llms.databricks (top-level), langchain_community.chat_models.mlflow (top-level), langchain_community.llms.google_palm (top-level), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.dependencies.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), huggingface_hub._webhooks_payload (conditional), langchain_community.llms.javelin_ai_gateway (top-level), langchain_community.llms.minimax (top-level), langchain_community.llms.mlflow_ai_gateway (top-level), langchain_community.llms.oci_generative_ai (top-level), langchain_community.llms.outlines (top-level), langchain_community.llms.pipelineai (top-level), langchain_community.llms.predictionguard (top-level), langchain_community.llms.rwkv (top-level), langchain_community.llms.titan_takeoff (top-level), langchain_community.llms.vertexai (top-level), langchain_community.llms.volcengine_maas (top-level), langchain.chains.combine_documents.base (top-level), langchain.chains.combine_documents.map_reduce (top-level), langchain.chains.combine_documents.map_rerank (top-level), langchain.output_parsers.structured (top-level), langchain.output_parsers.yaml (top-level), langchain.chains.prompt_selector (top-level), langchain_community.tools.wikipedia.tool (top-level), langchain_community.utilities.wolfram_alpha (top-level), langchain_community.tools.yahoo_finance_news (top-level), langchain_community.tools.you.tool (top-level), langchain_community.utilities.you (top-level), langchain_community.utilities.zapier (top-level), langchain_community.tools.zenguard.tool (top-level), langchain_community.utilities.twilio (top-level), langchain.memory.entity (top-level), langchain.memory.summary (top-level), langchain.agents.agent_toolkits.vectorstore.toolkit (top-level), langchain_community.tools.amadeus.closest_airport (top-level), langchain_community.tools.amadeus.flight_search (top-level), langchain_community.tools.multion.close_session (top-level), langchain_community.tools.multion.create_session (top-level), langchain_community.tools.multion.update_session (top-level), langchain_community.chains.pebblo_retrieval.models (top-level), langchain_community.chains.pebblo_retrieval.utilities (top-level), langchain_community.chains.openapi.chain (top-level), langchain_community.utilities.dataforseo_api_search (top-level), langchain_community.utilities.dalle_image_generator (top-level), langchain_community.vectorstores.azure_cosmos_db_no_sql (top-level), langchain_community.embeddings.aleph_alpha (top-level), langchain_community.embeddings.openai (top-level), langchain_community.embeddings.ascend (top-level), langchain_community.embeddings.awa (top-level), langchain_community.embeddings.baichuan (top-level), langchain_community.embeddings.baidu_qianfan_endpoint (top-level), langchain_community.embeddings.bedrock (top-level), langchain_community.embeddings.bookend (top-level), langchain_community.embeddings.clarifai (top-level), langchain_community.embeddings.clova (top-level), langchain_community.embeddings.cohere (top-level), langchain_community.embeddings.dashscope (top-level), langchain_community.embeddings.mlflow (top-level), langchain_community.embeddings.deepinfra (top-level), langchain_community.embeddings.edenai (top-level), langchain_community.embeddings.embaas (top-level), langchain_community.embeddings.ernie (top-level), langchain_community.embeddings.fake (top-level), langchain_community.embeddings.fastembed (top-level), langchain_community.embeddings.gigachat (top-level), langchain_community.embeddings.google_palm (top-level), langchain_community.embeddings.gpt4all (top-level), langchain_community.embeddings.gradient_ai (top-level), langchain_community.embeddings.huggingface (top-level), langchain_community.embeddings.huggingface_hub (top-level), langchain_community.embeddings.hunyuan (top-level), langchain_community.embeddings.infinity (top-level), langchain_community.embeddings.infinity_local (top-level), langchain_community.embeddings.ipex_llm (top-level), langchain_community.embeddings.itrex (top-level), langchain_community.embeddings.javelin_ai_gateway (top-level), langchain_community.embeddings.jina (top-level), langchain_community.embeddings.johnsnowlabs (top-level), langchain_community.embeddings.laser (top-level), langchain_community.embeddings.llamacpp (top-level), langchain_community.embeddings.llamafile (top-level), langchain_community.embeddings.llm_rails (top-level), langchain_community.embeddings.localai (top-level), langchain_community.embeddings.minimax (top-level), langchain_community.embeddings.mlflow_gateway (top-level), langchain_community.embeddings.modelscope_hub (top-level), langchain_community.embeddings.mosaicml (top-level), langchain_community.embeddings.naver (top-level), langchain_community.embeddings.nemo (top-level), langchain_community.embeddings.nlpcloud (top-level), langchain_community.embeddings.oci_generative_ai (top-level), langchain_community.embeddings.ollama (top-level), langchain_community.embeddings.openvino (top-level), langchain_community.embeddings.optimum_intel (top-level), langchain_community.embeddings.oracleai (top-level), langchain_community.embeddings.ovhcloud (top-level), langchain_community.embeddings.premai (top-level), langchain_community.embeddings.sagemaker_endpoint (top-level), langchain_community.embeddings.sambanova (top-level), langchain_community.embeddings.solar (top-level), langchain_community.embeddings.spacy_embeddings (top-level), langchain_community.embeddings.sparkllm (top-level), langchain_community.embeddings.tensorflow_hub (top-level), langchain_community.embeddings.textembed (top-level), langchain_community.embeddings.titan_takeoff (top-level), langchain_community.embeddings.volcengine (top-level), langchain_community.embeddings.voyageai (top-level), langchain_community.embeddings.yandex (top-level), langchain_community.embeddings.zhipuai (top-level), langchain_community.vectorstores.redis.schema (top-level), langchain_community.vectorstores.tencentvectordb (top-level), langchain_core.structured_query (top-level), langchain.chains.query_constructor.schema (top-level), langchain_community.document_transformers.embeddings_redundant_filter (top-level), langchain_community.document_transformers.long_context_reorder (top-level), langchain_community.tools.nuclia.tool (top-level), langchain_community.document_transformers.openai_functions (top-level), langchain.retrievers.document_compressors.listwise_rerank (top-level), langchain_community.retrievers.bedrock (top-level), langchain_community.retrievers.google_vertex_ai_search (top-level), langchain_community.retrievers.kendra (top-level), langchain_community.retrievers.needle (top-level), langchain_community.retrievers.web_research (top-level), langchain.chains.constitutional_ai.models (top-level), langchain_openai.chat_models.azure (top-level), langchain_openai.chat_models.base (top-level), langchain_openai.embeddings.base (top-level), langsmith.evaluation.string_evaluator (top-level), langchain.smith.evaluation.config (top-level), langchain_core.prompts.structured (top-level), langchain_core.runnables.fallbacks (top-level), langchain_core.runnables.passthrough (top-level), langchain_core.runnables.branch (top-level), langchain_core.runnables.history (top-level), langchain_community.chat_models.azure_openai (top-level), langchain_community.chat_models.baichuan (top-level), langchain_community.chat_models.llamacpp (top-level), langchain_community.chat_models.baidu_qianfan_endpoint (top-level), langchain_community.chat_models.deepinfra (top-level), langchain_community.chat_models.edenai (top-level), langchain_community.chat_models.google_palm (top-level), langchain_community.chat_models.gpt_router (top-level), langchain_community.chat_models.javelin_ai_gateway (top-level), langchain_community.chat_models.kinetica (top-level), langchain_community.chat_models.litellm (top-level), litellm.types.utils (top-level), litellm.types.llms.base (top-level), litellm.types.llms.openai (top-level), litellm.types.guardrails (top-level), litellm.types.rerank (top-level), litellm.types.vector_stores (top-level), litellm.types.router (top-level), litellm.types.completion (top-level), litellm.types.embedding (top-level), litellm.litellm_core_utils.litellm_logging (top-level), litellm.caching.caching (top-level), litellm.types.caching (top-level), litellm.caching.in_memory_cache (top-level), litellm.types.services (top-level), litellm.proxy._types (top-level), litellm.types.integrations.slack_alerting (top-level), litellm.integrations.custom_logger (top-level), litellm.router (top-level), litellm.types.integrations.prometheus (top-level), litellm.utils (top-level), litellm.caching.caching_handler (top-level), litellm.types.llms.databricks (top-level), litellm.llms.base_llm.chat.transformation (top-level), litellm.llms.base_llm.base_utils (top-level), litellm.types.llms.anthropic (top-level), litellm.litellm_core_utils.streaming_handler (top-level), litellm.types.llms.bedrock (top-level), litellm.types.llms.ollama (top-level), litellm.llms.bedrock.base_aws_llm (top-level), litellm.llms.openai.openai (top-level), litellm.llms.vertex_ai.gemini.transformation (top-level), litellm.proxy.spend_tracking.spend_tracking_utils (top-level), litellm.proxy.hooks.parallel_request_limiter (top-level), litellm.proxy.auth.auth_checks (top-level), litellm.proxy.management_endpoints.team_endpoints (top-level), litellm.types.proxy.management_endpoints.common_daily_activity (top-level), litellm.types.tag_management (top-level), litellm.proxy.management_endpoints.model_management_endpoints (top-level), litellm.types.proxy.management_endpoints.team_endpoints (top-level), litellm.router_utils.add_retry_fallback_headers (top-level), litellm.scheduler (top-level), litellm.types.passthrough_endpoints.vertex_ai (top-level), litellm.cost_calculator (top-level), litellm.types.mcp_server.mcp_server_manager (top-level), litellm.types.mcp_server.tool_registry (top-level), litellm.main (top-level), litellm.llms.azure.audio_transcriptions (top-level), litellm.llms.bedrock.image.image_handler (top-level), litellm.llms.groq.chat.transformation (top-level), litellm.llms.openai.transcriptions.handler (top-level), litellm.types.llms.watsonx (top-level), litellm.proxy.common_utils.swagger_utils (top-level), litellm.proxy.guardrails.guardrail_endpoints (top-level), litellm.proxy.guardrails.guardrail_hooks.aim (top-level), litellm.proxy.hooks.user_management_event_hooks (top-level), litellm.types.proxy.management_endpoints.internal_user_endpoints (top-level), litellm.types.proxy.management_endpoints.scim_v2 (top-level), litellm.types.integrations.arize_phoenix (top-level), litellm.types.integrations.arize (top-level), litellm.integrations.deepeval.types (top-level), litellm.integrations.argilla (top-level), litellm.integrations.braintrust_logging (top-level), litellm.integrations.galileo (top-level), litellm.integrations.langsmith (top-level), litellm.types.integrations.langsmith (top-level), litellm.integrations.prompt_layer (top-level), litellm.llms.databricks.chat.transformation (top-level), litellm.llms.vertex_ai.vertex_embeddings.transformation (top-level), litellm.llms.ollama.chat.transformation (top-level), langchain_community.chat_models.minimax (top-level), langchain_community.chat_models.mlflow_ai_gateway (top-level), langchain_community.llms.moonshot (top-level), langchain_community.chat_models.oci_data_science (top-level), langchain_community.chat_models.oci_generative_ai (top-level), langchain_community.chat_models.outlines (top-level), langchain_community.chat_models.perplexity (top-level), langchain_community.chat_models.premai (top-level), langchain_community.chat_models.reka (top-level), langchain_community.chat_models.sambanova (top-level), langchain_community.llms.solar (top-level), langchain_community.chat_models.tongyi (top-level), langchain_community.chat_models.yuan2 (top-level), langchain_community.chat_models.zhipuai (top-level), langchain_community.example_selectors.ngram_overlap (top-level), langchain_litellm.chat_models.litellm (top-level)
missing module named orjson.loads - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.dumps - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.JSONDecodeError - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.Fragment - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_UUID - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_NUMPY - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_DATACLASS - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_NON_STR_KEYS - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional), aiohttp.compression_utils (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional), aiohttp.compression_utils (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level), httpx._client (delayed, conditional, optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level), fsspec.implementations.http_sync (delayed, optional)
missing module named vcr - imported by langsmith.utils (delayed, optional)
missing module named trio - imported by tenacity.asyncio (delayed, conditional), httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level), openai.resources.vector_stores.file_batches (delayed, conditional)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named exceptiongroup - imported by anyio._core._exceptions (conditional), anyio._core._sockets (conditional), starlette._utils (conditional, optional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by aiohttp.worker (delayed), anyio._backends._asyncio (delayed, conditional), uvicorn.loops.auto (delayed, optional), uvicorn.loops.uvloop (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.config' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'rich.console' - imported by httpx._main (top-level), streamlit.error_util (delayed)
missing module named 'pygments.util' - imported by httpx._main (top-level)
missing module named pygments - imported by httpx._main (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named cython - imported by scipy._lib._testutils (optional), pydantic.v1.version (optional), pyarrow.conftest (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), fastapi.openapi.models (optional), pydantic.v1._hypothesis_plugin (optional)
missing module named langchain_anthropic - imported by langsmith.client (delayed, optional)
missing module named pydantic.validate_arguments - imported by pydantic (top-level), langchain_core.tools.base (top-level)
missing module named 'spacy.lang' - imported by langchain_text_splitters.spacy (delayed, conditional)
missing module named spacy - imported by langchain_text_splitters.spacy (delayed, optional), langchain_community.embeddings.spacy_embeddings (delayed, optional)
missing module named sentence_transformers - imported by langchain_text_splitters.sentence_transformers (delayed, optional), langchain_community.embeddings.huggingface (delayed, optional), langchain_community.embeddings.ipex_llm (delayed, optional), langchain_community.embeddings.self_hosted_hugging_face (delayed, conditional)
missing module named 'nltk.tokenize' - imported by langchain_text_splitters.nltk (delayed, conditional, optional)
missing module named konlpy - imported by langchain_text_splitters.konlpy (delayed, optional)
missing module named 'nltk.corpus' - imported by langchain_text_splitters.html (delayed, conditional, optional)
missing module named nltk - imported by langchain_text_splitters.html (delayed, conditional, optional)
missing module named lxml - imported by pandas.io.xml (conditional), langchain_text_splitters.html (delayed, optional), langchain_community.document_loaders.docugami (delayed, conditional, optional), langchain_community.document_loaders.sitemap (delayed, optional), langchain_community.document_loaders.evernote (delayed, optional)
missing module named 'bs4.element' - imported by langchain_text_splitters.html (delayed), langchain_community.document_loaders.readthedocs (delayed, conditional)
missing module named bs4 - imported by pandas.io.html (delayed), langchain_text_splitters.html (delayed, optional), langchain_community.document_loaders.arcgis_loader (delayed, optional), langchain_community.document_loaders.async_html (delayed), langchain_community.document_loaders.web_base (delayed), langchain_community.document_loaders.html_bs (delayed, optional), langchain_community.document_loaders.parsers.grobid (delayed, optional), langchain_community.document_loaders.parsers.html.bs4 (delayed, optional), langchain_community.document_loaders.blackboard (delayed, optional), langchain_community.document_loaders.chm (delayed), langchain_community.document_loaders.confluence (delayed, conditional, optional), langchain_community.document_loaders.sitemap (delayed, conditional, optional), langchain_community.document_loaders.gitbook (top-level), langchain_community.document_loaders.mhtml (delayed, optional), langchain_community.document_loaders.readthedocs (delayed, conditional, optional), langchain_community.document_loaders.recursive_url_loader (delayed, optional), langchain_community.document_loaders.trello (delayed, optional), langchain_community.tools.gmail.utils (delayed, optional), langchain_community.tools.office365.utils (delayed, optional), langchain_community.tools.playwright.extract_hyperlinks (delayed, optional), langchain_community.tools.playwright.extract_text (delayed, optional), langchain_community.utilities.steam (delayed), langchain_community.chains.llm_requests (delayed, optional), langchain_community.document_transformers.beautiful_soup_transformer (delayed, optional)
missing module named transformers - imported by langchain_text_splitters.base (delayed, optional), langchain_community.document_loaders.parsers.audio (delayed, optional), langchain_community.document_loaders.image_captions (delayed, optional), langchain_community.llms.ipex_llm (delayed, conditional, optional), langchain_community.llms.bigdl_llm (delayed, optional), langchain_community.llms.ctranslate2 (delayed, optional), langchain_community.llms.huggingface_pipeline (delayed, optional), langchain_community.llms.outlines (delayed, conditional), langchain_community.llms.petals (delayed, optional), langchain_community.llms.self_hosted_hugging_face (delayed), langchain_community.llms.weight_only_quantization (delayed, optional), langchain_community.agent_toolkits.load_tools (delayed, optional), langchain_community.embeddings.openai (delayed, conditional, optional), langchain_community.embeddings.ascend (delayed, optional), langchain_community.embeddings.itrex (delayed), langchain_community.embeddings.openvino (delayed, optional), langchain_community.embeddings.optimum_intel (delayed, optional), langchain_openai.embeddings.base (delayed, conditional, optional), langchain_core.language_models.base (delayed, optional), langchain_community.chat_models.huggingface (delayed), litellm.llms.petals.completion.handler (delayed, conditional, optional), langchain_community.chat_models.outlines (delayed, conditional)
missing module named collections.MutableSequence - imported by collections (optional), jsonpatch (optional)
missing module named collections.MutableMapping - imported by collections (conditional), requests_toolbelt._compat (conditional), jsonpatch (optional)
missing module named collections.Sequence - imported by collections (conditional), pyautogui (conditional), jsonpatch (optional)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named rich - imported by pydantic._internal._core_utils (delayed), streamlit.error_util (delayed), streamlit.config (delayed, optional)
missing module named pyppeteer - imported by langchain_core.runnables.graph_mermaid (delayed, optional)
missing module named pygraphviz - imported by langchain_core.runnables.graph_png (delayed, optional), langchain_community.graphs.networkx_graph (delayed, optional)
missing module named 'grandalf.routing' - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named 'grandalf.layouts' - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named grandalf - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named simsimd - imported by langchain_core.vectorstores.utils (delayed, optional), langchain_community.utils.math (delayed, optional), langsmith._internal._embedding_distance (delayed, optional)
missing module named defusedxml - imported by PIL.Image (optional), langchain_core.output_parsers.xml (delayed, conditional, optional)
missing module named rapidfuzz - imported by langchain.evaluation.parsing.json_distance (delayed, conditional, optional), langchain.evaluation.string_distance.base (delayed, optional), langsmith._internal._edit_distance (delayed, optional)
missing module named langchain.chains.VectorDBQAWithSourcesChain - imported by langchain.chains (delayed, conditional), langchain (delayed, conditional)
missing module named langchain.chains.VectorDBQA - imported by langchain.chains (delayed, conditional), langchain (delayed, conditional)
missing module named langchain.chains.QAWithSourcesChain - imported by langchain.chains (delayed, conditional), langchain (delayed, conditional)
missing module named langchain.chains.LLMMathChain - imported by langchain.chains (delayed, conditional), langchain (delayed, conditional)
missing module named langchain.chains.LLMCheckerChain - imported by langchain.chains (delayed, conditional), langchain (delayed, conditional)
missing module named langchain.chains.ConversationChain - imported by langchain.chains (delayed, conditional), langchain (delayed, conditional)
missing module named langchain.chains.ReduceDocumentsChain - imported by langchain.chains (top-level), langchain.chains.question_answering.chain (top-level), langchain.chains.qa_with_sources.base (top-level), langchain.chains.loading (top-level)
missing module named langchain.chains.LLMChain - imported by langchain.chains (top-level), langchain.chains.openai_functions.base (top-level), langchain.agents.mrkl.base (top-level), langchain.agents.conversational_chat.base (top-level), langchain.agents.conversational.base (top-level), langchain_community.chains.llm_requests (top-level), langchain (delayed, conditional), langchain.retrievers.document_compressors.chain_filter (top-level), langchain_community.retrievers.web_research (top-level)
missing module named guardrails - imported by langchain_community.output_parsers.rail_parser (delayed, optional)
missing module named rpds.List - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieSet - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieMap - imported by rpds (top-level), referencing._core (top-level), jsonschema._types (top-level), jsonschema.validators (top-level)
missing module named annotationlib - imported by sqlalchemy.util.langhelpers (conditional), attr._compat (conditional)
missing module named isoduration - imported by jsonschema._format (top-level)
missing module named uri_template - imported by jsonschema._format (top-level)
missing module named webcolors - imported by jsonschema._format (top-level)
missing module named rfc3339_validator - imported by jsonschema._format (top-level)
missing module named rfc3986_validator - imported by jsonschema._format (optional)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named fqdn - imported by jsonschema._format (top-level)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named websockets.speedups - imported by websockets.frames (optional), websockets.legacy.framing (optional)
missing module named 'websockets.asyncio' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional), litellm.litellm_core_utils.realtime_streaming (conditional), litellm.llms.custom_httpx.llm_http_handler (delayed), litellm.llms.azure.realtime.handler (delayed), litellm.llms.openai.realtime.handler (delayed), litellm.proxy.guardrails.guardrail_hooks.aim (top-level)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named datasets - imported by langchain_community.document_loaders.hugging_face_dataset (delayed, optional), langchain.evaluation.loading (delayed, optional)
missing module named zhipuai - imported by langchain_community.embeddings.zhipuai (delayed, optional)
missing module named 'yandex.cloud' - imported by langchain_community.llms.yandex (delayed, optional), langchain_community.embeddings.yandex (delayed, optional), langchain_community.chat_models.yandex (delayed, optional)
missing module named grpc - imported by langchain_community.llms.yandex (delayed, optional), langchain_community.embeddings.yandex (delayed, optional), langchain_community.vectorstores.qdrant (delayed), langchain_community.retrievers.qdrant_sparse_vector_retriever (delayed, optional), langchain_community.vectorstores.vald (delayed, optional), langchain_community.chat_models.yandex (delayed, optional)
missing module named xinference_client - imported by langchain_community.llms.xinference (delayed, optional), langchain_community.embeddings.xinference (delayed, optional)
missing module named 'xinference.client' - imported by langchain_community.llms.xinference (delayed, optional), langchain_community.embeddings.xinference (delayed, optional)
missing module named 'volcengine.maas' - imported by langchain_community.embeddings.volcengine (delayed, optional)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional), huggingface_hub._login (delayed, optional), pydeck.io.html (delayed), altair.vegalite.v4.display (delayed), altair.vegalite.v4.api (delayed), altair.vegalite.v3.display (delayed), altair.vegalite.v3.api (delayed), altair.vega.v5.display (delayed), langchain.smith.evaluation.runner_utils (delayed, conditional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), pydeck.widget.widget (top-level)
missing module named setuptools_scm - imported by pyarrow (optional), tqdm.version (optional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.groupby.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core._numba.extensions (top-level)
missing module named 'google.auth' - imported by pandas.io.gbq (conditional), langchain_community.utilities.vertexai (conditional), langchain_community.document_loaders.bigquery (conditional), langchain_community.document_loaders.googledrive (delayed, optional), langchain_community.document_loaders.youtube (delayed, optional), langchain_community.tools.gmail.utils (conditional), litellm.llms.vertex_ai.vertex_llm_base (delayed, conditional), litellm.llms.vertex_ai.vertex_ai_non_gemini (delayed, conditional, optional)
missing module named 'sphinx.ext' - imported by pyarrow.vendored.docscrape (delayed, conditional)
missing module named 'pandas.api.internals' - imported by pyarrow.pandas_compat (delayed, conditional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), pyarrow.conftest (optional)
missing module named pytest - imported by scipy._lib._testutils (delayed), scipy._lib._array_api (delayed), scipy._lib.array_api_extra.testing (conditional), pandas._testing._io (delayed), pandas._testing (delayed), langsmith.testing._internal (optional), fsspec.conftest (top-level), pyarrow.conftest (top-level), pyarrow.tests.util (top-level)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named dummy_threading - imported by psutil._compat (optional), requests.cookies (optional)
missing module named simplejson - imported by requests.compat (conditional, optional), huggingface_hub.utils._fixes (optional)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named paramiko - imported by fsspec.implementations.sftp (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fastapi.responses (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (conditional), scipy._lib.array_api_extra._lib._lazy (delayed, conditional), scipy._lib.array_api_extra.testing (delayed), fsspec.implementations.dask (top-level)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'pyarrow._azurefs' - imported by pyarrow.fs (optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named 'psycopg.pq' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed)
missing module named 'psycopg.types' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named 'psycopg.adapt' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named psycopg - imported by langchain_community.chat_message_histories.postgres (delayed), sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named oracledb - imported by langchain_community.document_loaders.oracleadb_loader (delayed, optional), langchain_community.document_loaders.oracleai (delayed, conditional, optional), langchain_community.utilities.oracleai (delayed, conditional, optional), langchain_community.embeddings.oracleai (delayed, conditional, optional), langchain_community.vectorstores.oraclevs (delayed, conditional, optional), sqlalchemy.dialects.oracle.oracledb (delayed, conditional)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named 'mysql.connector' - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed, conditional, optional)
missing module named mysql - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named 'pymysql.constants' - imported by sqlalchemy.dialects.mysql.aiomysql (delayed)
missing module named psycopg2 - imported by sqlalchemy.dialects.postgresql.psycopg2 (delayed), langchain_community.vectorstores.yellowbrick (delayed), sqlalchemy (top-level)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed), langchain.chains.llm_math.base (delayed, optional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional), langchain_community.document_loaders.s3_file (conditional), langchain_community.document_loaders.s3_directory (conditional), langchain_community.graphs.neptune_graph (delayed, conditional, optional), langchain_community.graphs.neptune_rdf_graph (delayed, conditional, optional), litellm.caching.s3_cache (delayed)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'openpyxl.cell' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.styles' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.workbook' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.descriptors' - imported by pandas.io.excel._openpyxl (conditional)
missing module named openpyxl - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional), langchain_community.document_loaders.confluence (delayed, optional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named 'matplotlib.pyplot' - imported by scipy.stats._fit (delayed, conditional), scipy.stats._survival (delayed, conditional), scipy.stats._distribution_infrastructure (delayed, optional), pandas.io.formats.style (optional), tqdm.gui (delayed), streamlit.elements.pyplot (delayed, optional)
missing module named matplotlib - imported by scipy.spatial._plotutils (delayed), scipy.stats._fit (delayed, optional), scipy.stats._survival (delayed, optional), pandas.io.formats.style (optional), tqdm.gui (delayed), streamlit.elements.plotly_chart (conditional)
missing module named 'matplotlib.colors' - imported by pandas.io.formats.style (conditional), pandas.plotting._misc (conditional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional), pydeck.widget.widget (top-level)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level), pandas.io.formats.printing (delayed, conditional), altair.utils.core (delayed, conditional), altair._magics (top-level)
missing module named IPython - imported by dotenv.ipython (top-level), pandas.io.formats.printing (delayed), altair._magics (top-level), langchain.smith.evaluation.runner_utils (delayed, optional)
missing module named StringIO - imported by six (conditional), pydub.audio_segment (optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), requests_toolbelt._compat (conditional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named 'jax.experimental' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.numpy' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_extra.testing (delayed)
missing module named cupy - imported by scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named torch - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.torch (top-level), scipy._lib.array_api_compat.torch._aliases (top-level), scipy._lib.array_api_compat.torch._info (top-level), scipy._lib.array_api_compat.torch._typing (top-level), langchain_community.document_loaders.parsers.audio (delayed, optional), huggingface_hub.hub_mixin (conditional), huggingface_hub.serialization._torch (delayed, conditional, optional), langchain_community.llms.huggingface_pipeline (delayed, conditional), langchain_community.llms.self_hosted (delayed, conditional), langchain_community.llms.self_hosted_hugging_face (delayed, conditional), langchain_community.embeddings.ascend (delayed, optional), langchain_community.embeddings.itrex (delayed, optional), langchain_community.embeddings.openvino (delayed, optional), langchain_community.embeddings.optimum_intel (delayed, optional), langchain_community.embeddings.self_hosted_hugging_face (delayed, conditional)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (conditional)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib._array_api (delayed, conditional), scipy._lib.array_api_extra._lib._utils._helpers (delayed, conditional), scipy._lib.array_api_extra._lib._lazy (delayed, conditional)
missing module named scipy._lib.array_api_compat.common.array_namespace - imported by scipy._lib.array_api_compat.common (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level)
missing module named Cython - imported by scipy._lib._testutils (optional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level)
missing module named 'dask.typing' - imported by scipy._lib.array_api_extra.testing (conditional)
missing module named array_api_compat - imported by scipy._lib.array_api_extra._lib._utils._compat (optional)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.special.comb - imported by scipy.special (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._rbfinterp (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._resampling (top-level), scipy.linalg._special_matrices (delayed)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named 'matplotlib.ticker' - imported by scipy.stats._fit (delayed)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named 'matplotlib.collections' - imported by scipy.spatial._plotutils (delayed)
missing module named scikits - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.optimize.minimize - imported by scipy.optimize (delayed, conditional, optional), scipy._lib.pyprima.common._project (delayed, conditional, optional), scipy.optimize._differentialevolution (top-level), scipy.optimize._shgo (top-level), scipy.optimize._dual_annealing (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.special.betainc - imported by scipy.special (top-level), scipy.stats._quantile (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.stats._multivariate (top-level), scipy.fft._fftlog_backend (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.psi - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.gammainc - imported by scipy.special (top-level), scipy.stats._qmc (top-level)
missing module named scipy.special.ndtri - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._binomtest (top-level), scipy.stats._relative_risk (top-level), scipy.stats._odds_ratio (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ndtr - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.kv - imported by scipy.special (top-level), scipy.stats._hypotests (top-level)
missing module named scipy.special.gamma - imported by scipy.special (top-level), scipy.stats._hypotests (top-level)
missing module named scipy.special.zeta - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.gammaln - imported by scipy.special (top-level), scipy.integrate._quadrature (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._hypotests (top-level), scipy.stats._multivariate (top-level), scipy.optimize._dual_annealing (top-level), scipy.special._spfun_stats (top-level)
missing module named scipy.special.roots_legendre - imported by scipy.special (top-level), scipy.integrate._quadrature (top-level), scipy.integrate._rules._gauss_legendre (top-level)
missing module named scipy.special.entr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.xlogy - imported by scipy.special (top-level), scipy.interpolate._rbf (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.factorial - imported by scipy.special (top-level), scipy.interpolate._polyint (top-level), scipy.stats._resampling (top-level)
missing module named scipy.special.poch - imported by scipy.special (top-level), scipy.interpolate._bsplines (top-level), scipy.fft._fftlog_backend (top-level)
missing module named scipy.special.rel_entr - imported by scipy.special (top-level), scipy.spatial.distance (top-level)
missing module named scipy.linalg.lu_solve - imported by scipy.linalg (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.linalg.lu_factor - imported by scipy.linalg (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.linalg.cho_solve_banded - imported by scipy.linalg (top-level), scipy.interpolate._bsplines (top-level)
missing module named scipy.linalg.cholesky_banded - imported by scipy.linalg (top-level), scipy.interpolate._bsplines (top-level)
missing module named scipy.linalg.solve_banded - imported by scipy.linalg (top-level), scipy.spatial.transform._rotation_spline (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named scipy.linalg.inv - imported by scipy.linalg (top-level), scipy.optimize._nonlin (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.solve - imported by scipy.linalg (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._linprog_rs (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.cho_factor - imported by scipy.linalg (top-level), scipy.optimize._lsq.common (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.svd - imported by scipy.linalg (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._remove_redundancy (top-level), scipy.linalg._decomp_polar (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.sparse.linalg._eigen._svds (top-level)
missing module named scipy.linalg.qr - imported by scipy.linalg (top-level), scipy._lib.cobyqa.subsolvers.optim (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._nonlin (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.eigh - imported by scipy.linalg (top-level), scipy._lib.cobyqa.models (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.cho_solve - imported by scipy.linalg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._lsq.common (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.solve_triangular - imported by scipy.linalg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named scipy.linalg.cholesky - imported by scipy.linalg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._minpack_py (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named 'scikits.umfpack' - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.linalg.onenormest - imported by scipy.sparse.linalg (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named scipy.sparse.linalg.splu - imported by scipy.sparse.linalg (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.sparse.linalg.aslinearoperator - imported by scipy.sparse.linalg (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.linalg.interpolative (delayed), scipy.sparse.linalg._svdp (top-level), scipy.sparse.linalg._expm_multiply (top-level), scipy.sparse.linalg._onenormest (top-level)
missing module named scipy.sparse.linalg.lsmr - imported by scipy.sparse.linalg (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._lsq.trf_linear (top-level)
missing module named scipy.sparse.linalg.LinearOperator - imported by scipy.sparse.linalg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._differentiable_functions (top-level), scipy.optimize._trustregion_constr.minimize_trustregion_constr (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.tr_interior_point (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.linalg.interpolative (delayed), scipy.sparse.csgraph._laplacian (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.linalg._sketches (top-level)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.common (top-level), scipy.stats._crosstab (top-level), pandas.core.arrays.sparse.accessor (delayed)
missing module named scipy.sparse.vstack - imported by scipy.sparse (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.block_array - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.csr_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.find - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level)
missing module named scipy.sparse.isspmatrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._milp (top-level), scipy.linalg._sketches (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), pandas.core.dtypes.common (delayed, conditional, optional), scipy.sparse.csgraph._validation (top-level)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named 'matplotlib.table' - imported by pandas.plotting._misc (conditional)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional), streamlit.elements.pyplot (conditional)
missing module named 'matplotlib.axes' - imported by pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas._testing.asserters (delayed)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named 'lxml.etree' - imported by pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
missing module named 'google.api_core' - imported by langchain_community.utilities.vertexai (delayed, conditional, optional), langchain_community.document_loaders.parsers.docai (delayed, conditional, optional), langchain_community.document_loaders.google_speech_to_text (delayed, optional), langchain_community.llms.vertexai (delayed, optional), langchain_community.utils.google (delayed), langchain_community.vectorstores.bigquery_vector_search (delayed), langchain_community.embeddings.google_palm (delayed), langchain_community.embeddings.vertexai (delayed), langchain_community.document_transformers.google_translate (delayed, optional), langchain_community.retrievers.google_vertex_ai_search (delayed, conditional, optional), langchain_community.chat_models.google_palm (delayed)
missing module named 'vertexai.language_models' - imported by langchain_community.llms.vertexai (delayed, conditional, optional), langchain_community.embeddings.vertexai (delayed, conditional, optional), litellm.llms.vertex_ai.vertex_ai_non_gemini (delayed, optional), langchain_community.chat_models.vertexai (delayed, conditional, optional)
missing module named 'vertexai.preview' - imported by langchain_community.utilities.vertexai (delayed), langchain_community.llms.vertexai (delayed, conditional, optional), litellm.llms.vertex_ai.vertex_ai_non_gemini (delayed, optional), langchain_community.chat_models.vertexai (delayed, conditional, optional)
missing module named 'google.cloud' - imported by langchain_community.utilities.vertexai (delayed, optional), langchain_community.document_loaders.bigquery (delayed, optional), langchain_community.document_loaders.parsers.docai (delayed, conditional, optional), langchain_community.document_loaders.gcs_file (delayed, optional), langchain_community.document_loaders.gcs_directory (delayed, optional), langchain_community.document_loaders.google_speech_to_text (delayed, conditional, optional), langchain_community.tools.google_cloud.texttospeech (delayed, conditional, optional), langchain_community.llms.vertexai (delayed, conditional, optional), langchain_community.chat_message_histories.firestore (conditional), langchain_community.vectorstores.bigquery_vector_search (delayed, conditional, optional), langchain_community.vectorstores.matching_engine (delayed, conditional, optional), langchain_community.document_transformers.google_translate (delayed, optional), langchain_community.retrievers.google_cloud_documentai_warehouse (delayed, conditional, optional), langchain_community.retrievers.google_vertex_ai_search (delayed, conditional, optional), litellm.llms.vertex_ai.vertex_ai_non_gemini (delayed, conditional, optional), litellm.secret_managers.google_kms (delayed, optional)
missing module named vertexai - imported by langchain_community.utilities.vertexai (delayed, conditional, optional), litellm.llms.vertex_ai.vertex_ai_partner_models.main (delayed, optional), litellm.llms.vertex_ai.vertex_ai_non_gemini (delayed, optional), litellm.llms.vertex_ai.vertex_model_garden.main (delayed, optional)
missing module named langchain_community.llms.Writer - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.StochasticAI - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.SagemakerEndpoint - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.PipelineAI - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.Petals - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.OpenAI - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.Modal - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.LlamaCpp - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional), langchain_community.retrievers.web_research (top-level)
missing module named langchain_community.llms.HuggingFaceTextGenInference - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.HuggingFaceHub - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.GooseAI - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.ForefrontAI - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.Cohere - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.CerebriumAI - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.Banana - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named langchain_community.llms.Anthropic - imported by langchain_community.llms (delayed, conditional), langchain (delayed, conditional)
missing module named sseclient - imported by langchain_community.llms.sambanova (delayed, optional), langchain_community.llms.you (delayed, optional), langchain_community.chat_models.sambanova (delayed, optional)
missing module named websocket - imported by langchain_community.llms.textgen (delayed, optional), langchain_community.llms.sparkllm (delayed, optional), langchain_community.chat_models.sparkllm (delayed, optional)
missing module named volcengine - imported by langchain_community.llms.volcengine_maas (delayed, optional)
missing module named yandex - imported by langchain_community.llms.yandex (delayed, optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named 'xinference.model' - imported by langchain_community.llms.xinference (conditional)
missing module named xinference - imported by langchain_community.llms.xinference (conditional)
missing module named writerai - imported by langchain_community.llms.writer (delayed, optional)
missing module named 'intel_extension_for_transformers.utils' - imported by langchain_community.llms.weight_only_quantization (delayed, optional)
missing module named intel_extension_for_transformers - imported by langchain_community.llms.weight_only_quantization (delayed, optional)
missing module named ibm_watsonx_ai - imported by langchain_community.llms.watsonxllm (delayed, optional)
missing module named vllm - imported by langchain_community.llms.vllm (delayed, optional), litellm.llms.vllm.completion.handler (delayed, optional)
missing module named dashscope - imported by langchain_community.llms.tongyi (delayed, optional), langchain_community.embeddings.dashscope (delayed, optional), langchain_community.chat_models.tongyi (delayed, optional)
missing module named takeoff_client - imported by langchain_community.llms.titan_takeoff (delayed, optional), langchain_community.embeddings.titan_takeoff (delayed, optional)
missing module named runhouse - imported by langchain_community.llms.self_hosted (delayed, optional)
missing module named boto3 - imported by langchain_community.document_loaders.athena (delayed, optional), langchain_community.document_loaders.parsers.pdf (delayed, conditional, optional), langchain_community.document_loaders.pdf (delayed, conditional, optional), langchain_community.document_loaders.glue_catalog (delayed, optional), langchain_community.document_loaders.s3_file (delayed, optional), langchain_community.document_loaders.s3_directory (delayed, optional), langchain_community.utilities.awslambda (delayed, optional), langchain_community.llms.bedrock (delayed, optional), langchain_community.llms.sagemaker_endpoint (delayed, optional), langchain_community.chat_message_histories.dynamodb (delayed, conditional, optional), langchain_community.graphs.neptune_graph (delayed, conditional, optional), langchain_community.graphs.neptune_rdf_graph (delayed, conditional, optional), langchain_community.embeddings.bedrock (delayed, optional), langchain_community.embeddings.sagemaker_endpoint (delayed, optional), langchain_community.retrievers.bedrock (delayed, optional), langchain_community.retrievers.kendra (delayed, optional), litellm.llms.bedrock.base_aws_llm (delayed), litellm.llms.bedrock.common_utils (delayed), litellm.proxy.auth.rds_iam_token (delayed), litellm.proxy.common_utils.load_config_utils (delayed, optional), litellm.llms.sagemaker.completion.handler (delayed), litellm.secret_managers.aws_secret_manager (delayed, optional), litellm.caching.s3_cache (delayed), litellm.integrations.dynamodb (delayed), litellm.integrations.s3 (delayed)
missing module named 'rwkv.utils' - imported by langchain_community.llms.rwkv (delayed, optional)
missing module named rwkv - imported by langchain_community.llms.rwkv (delayed, optional)
missing module named sentencepiece_model_pb2 - imported by tokenizers.implementations.sentencepiece_unigram (delayed, optional)
missing module named replicate - imported by langchain_community.llms.replicate (delayed, conditional, optional)
missing module named 'promptlayer.utils' - imported by langchain_community.llms.promptlayer_openai (delayed), langchain_community.chat_models.promptlayer_openai (delayed)
missing module named promptlayer - imported by langchain_community.llms.promptlayer_openai (delayed)
missing module named predictionguard - imported by langchain_community.llms.predictionguard (delayed, optional)
missing module named 'semantic_version.base' - imported by langchain_community.llms.predibase (delayed, optional)
missing module named 'predibase.version' - imported by langchain_community.llms.predibase (delayed, optional)
missing module named semantic_version - imported by langchain_community.llms.predibase (delayed, optional)
missing module named 'lorax.types' - imported by langchain_community.llms.predibase (delayed)
missing module named 'lorax.errors' - imported by langchain_community.llms.predibase (delayed)
missing module named lorax - imported by langchain_community.llms.predibase (delayed)
missing module named 'predibase.resource' - imported by langchain_community.llms.predibase (delayed, conditional, optional)
missing module named 'predibase.pql' - imported by langchain_community.llms.predibase (delayed, conditional, optional)
missing module named predibase - imported by langchain_community.llms.predibase (delayed, conditional, optional)
missing module named pipeline - imported by langchain_community.llms.pipelineai (delayed, optional)
missing module named petals - imported by langchain_community.llms.petals (delayed, optional), litellm.llms.petals.completion.handler (delayed, conditional, optional)
missing module named outlines - imported by langchain_community.llms.outlines (delayed, optional), langchain_community.chat_models.outlines (delayed)
missing module named openlm - imported by langchain_community.llms.openlm (delayed, optional)
missing module named opaqueprompts - imported by langchain_community.llms.opaqueprompts (delayed, optional)
missing module named 'oci.generative_ai_inference' - imported by langchain_community.llms.oci_generative_ai (delayed), langchain_community.embeddings.oci_generative_ai (delayed), langchain_community.chat_models.oci_generative_ai (delayed, optional)
missing module named oci - imported by langchain_community.llms.oci_generative_ai (delayed, optional), langchain_community.embeddings.oci_generative_ai (delayed, optional)
missing module named ads - imported by langchain_community.llms.oci_data_science_model_deployment_endpoint (delayed, optional)
missing module named nlpcloud - imported by langchain_community.llms.nlpcloud (delayed, optional), langchain_community.embeddings.nlpcloud (delayed, optional)
missing module named 'mlx_lm.utils' - imported by langchain_community.llms.mlx_pipeline (delayed, optional), langchain_community.chat_models.mlx (delayed, optional)
missing module named mlx - imported by langchain_community.llms.mlx_pipeline (delayed, optional)
missing module named 'mlx_lm.sample_utils' - imported by langchain_community.llms.mlx_pipeline (delayed, optional), langchain_community.chat_models.mlx (delayed, optional)
missing module named mlx_lm - imported by langchain_community.llms.mlx_pipeline (delayed, optional)
missing module named 'mlflow.gateway' - imported by langchain_community.llms.mlflow_ai_gateway (delayed, optional), langchain_community.embeddings.mlflow_gateway (delayed, optional), langchain_community.chat_models.mlflow_ai_gateway (delayed, optional)
missing module named 'mlflow.deployments' - imported by langchain_community.chat_models.mlflow (delayed, optional), langchain_community.llms.mlflow (delayed, optional), langchain_community.embeddings.mlflow (delayed, optional)
missing module named manifest - imported by langchain_community.llms.manifest (delayed, optional)
missing module named llama_cpp - imported by langchain_community.llms.llamacpp (delayed, optional), langchain_community.embeddings.llamacpp (delayed, conditional, optional), langchain_community.chat_models.llamacpp (delayed, optional)
missing module named konko - imported by langchain_community.llms.konko (delayed, optional), langchain_community.chat_models.konko (delayed, optional)
missing module named javelin_sdk - imported by langchain_community.llms.javelin_ai_gateway (delayed, optional), langchain_community.embeddings.javelin_ai_gateway (delayed, optional), langchain_community.chat_models.javelin_ai_gateway (delayed, optional)
missing module named 'transformers.tools' - imported by langchain_community.llms.ipex_llm (delayed, conditional)
missing module named 'transformers.generation' - imported by langchain_community.llms.ipex_llm (delayed, conditional)
missing module named ipex_llm - imported by langchain_community.llms.ipex_llm (delayed, optional)
missing module named text_generation - imported by langchain_community.llms.huggingface_text_gen_inference (delayed, optional)
missing module named 'optimum.intel' - imported by langchain_community.llms.huggingface_pipeline (delayed, conditional, optional), langchain_community.embeddings.openvino (delayed, optional), langchain_community.embeddings.optimum_intel (delayed, optional)
missing module named optimum - imported by langchain_community.llms.huggingface_pipeline (delayed, conditional, optional)
missing module named 'google.colab' - imported by huggingface_hub.utils._auth (delayed, optional)
missing module named 'torch_xla.core' - imported by huggingface_hub.serialization._torch (delayed, conditional, optional)
missing module named torch_xla - imported by huggingface_hub.serialization._torch (delayed, conditional)
missing module named 'torch.utils' - imported by huggingface_hub._tensorboard_logger (optional), huggingface_hub.serialization._torch (delayed, optional)
missing module named 'torch.distributed' - imported by huggingface_hub.serialization._torch (delayed, optional)
missing module named safetensors - imported by huggingface_hub.hub_mixin (conditional), huggingface_hub.serialization._torch (delayed, conditional, optional)
missing module named 'safetensors.torch' - imported by huggingface_hub.hub_mixin (conditional), huggingface_hub.serialization._torch (delayed, conditional, optional)
missing module named tensorflow - imported by langchain_community.utilities.tensorflow_datasets (delayed, optional), huggingface_hub.keras_mixin (conditional, optional), huggingface_hub.serialization._tensorflow (delayed, conditional)
missing module named hf_transfer - imported by huggingface_hub.file_download (delayed, conditional, optional), huggingface_hub.lfs (delayed, optional)
missing module named hf_xet - imported by huggingface_hub.file_download (delayed, optional), huggingface_hub._commit_api (delayed)
missing module named tf_keras - imported by huggingface_hub.keras_mixin (conditional, optional)
missing module named 'mcp.client' - imported by huggingface_hub.inference._mcp.mcp_client (delayed, conditional), litellm.proxy._experimental.mcp_server.mcp_server_manager (top-level)
missing module named mcp - imported by huggingface_hub.inference._mcp.utils (conditional), huggingface_hub.inference._mcp.mcp_client (delayed, conditional), litellm.proxy._experimental.mcp_server.mcp_server_manager (top-level)
missing module named fastai - imported by huggingface_hub.fastai_utils (delayed)
missing module named gradio - imported by huggingface_hub._webhooks_server (delayed, conditional)
missing module named tensorboardX - imported by huggingface_hub._tensorboard_logger (conditional, optional)
missing module named 'authlib.integrations' - imported by huggingface_hub._oauth (delayed, optional)
missing module named authlib - imported by huggingface_hub._oauth (delayed, optional), streamlit.auth_util (delayed, optional)
missing module named 'itsdangerous.exc' - imported by starlette.middleware.sessions (top-level)
missing module named itsdangerous - imported by starlette.middleware.sessions (top-level)
missing module named 'ipywidgets.widgets' - imported by huggingface_hub._login (delayed, optional)
missing module named 'InquirerPy.separator' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'InquirerPy.base' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named InquirerPy - imported by huggingface_hub.commands.delete_cache (optional)
missing module named gradientai - imported by langchain_community.llms.gradient_ai (delayed, optional), langchain_community.embeddings.gradient_ai (delayed, optional)
missing module named gpt4all - imported by langchain_community.llms.gpt4all (delayed, optional), langchain_community.embeddings.gpt4all (delayed, optional)
missing module named 'google.generativeai' - imported by langchain_community.llms.google_palm (delayed, optional), langchain_community.embeddings.google_palm (delayed, optional), langchain_community.chat_models.google_palm (delayed, conditional, optional), litellm.llms.deprecated_providers.palm (delayed, optional)
missing module named 'gigachat.models' - imported by langchain_community.llms.gigachat (conditional), langchain_community.chat_models.gigachat (delayed, conditional)
missing module named gigachat - imported by langchain_community.llms.gigachat (delayed, conditional, optional), langchain_community.embeddings.gigachat (delayed, optional)
missing module named friendli - imported by langchain_community.llms.friendli (delayed, optional)
missing module named 'fireworks.client' - imported by langchain_community.llms.fireworks (delayed), langchain_community.chat_models.fireworks (delayed, optional)
missing module named fireworks - imported by langchain_community.llms.fireworks (delayed, optional)
missing module named deepsparse - imported by langchain_community.llms.deepsparse (delayed, optional)
missing module named cloudpickle - imported by langchain_community.llms.databricks (delayed, optional)
missing module named 'dbruntime.databricks_repl_context' - imported by langchain_community.llms.databricks (delayed, optional)
missing module named mlflow - imported by langchain_community.llms.databricks (delayed, optional), litellm.integrations.mlflow (delayed)
missing module named ctranslate2 - imported by langchain_community.llms.ctranslate2 (delayed, optional)
missing module named ctransformers - imported by langchain_community.llms.ctransformers (delayed, optional)
missing module named cohere - imported by langchain_community.llms.cohere (delayed, optional), langchain_community.embeddings.cohere (delayed, optional), langchain.retrievers.document_compressors.cohere_rerank (delayed, conditional, optional)
missing module named 'clarifai.client' - imported by langchain_community.llms.clarifai (delayed, optional), langchain_community.vectorstores.clarifai (delayed, optional), langchain_community.embeddings.clarifai (delayed, optional)
missing module named clarifai - imported by langchain_community.llms.clarifai (delayed, optional)
missing module named 'bigdl.llm' - imported by langchain_community.llms.bigdl_llm (delayed, optional)
missing module named bigdl - imported by langchain_community.llms.bigdl_llm (delayed, optional)
missing module named anthropic - imported by langchain_community.llms.anthropic (delayed, optional), langchain_community.utilities.anthropic (delayed, optional), litellm.utils (delayed, conditional, optional), litellm.integrations.helicone (delayed)
missing module named beam - imported by langchain_community.llms.beam (delayed, optional)
missing module named banana_dev - imported by langchain_community.llms.bananadev (delayed, optional)
missing module named qianfan - imported by langchain_community.llms.baidu_qianfan_endpoint (delayed, optional), langchain_community.embeddings.baidu_qianfan_endpoint (delayed, optional), langchain_community.chat_models.baidu_qianfan_endpoint (delayed, optional)
missing module named aphrodite - imported by langchain_community.llms.aphrodite (delayed, optional)
missing module named aleph_alpha_client - imported by langchain_community.llms.aleph_alpha (delayed, optional), langchain_community.embeddings.aleph_alpha (delayed, optional)
missing module named tensorflow_text - imported by langchain_community.embeddings.tensorflow_hub (delayed, optional)
missing module named tensorflow_hub - imported by langchain_community.embeddings.tensorflow_hub (delayed, optional)
missing module named InstructorEmbedding - imported by langchain_community.embeddings.huggingface (delayed, optional), langchain_community.embeddings.self_hosted_hugging_face (delayed, conditional)
missing module named 'premai.models' - imported by langchain_community.embeddings.premai (delayed), langchain_community.chat_models.premai (delayed, conditional)
missing module named premai - imported by langchain_community.embeddings.premai (delayed, optional), langchain_community.chat_models.premai (delayed, optional)
missing module named 'modelscope.utils' - imported by langchain_community.embeddings.modelscope_hub (delayed, optional)
missing module named modelscope - imported by langchain_community.embeddings.modelscope_hub (delayed, optional)
missing module named model2vec - imported by langchain_community.embeddings.model2vec (delayed, optional)
missing module named laser_encoders - imported by langchain_community.embeddings.laser (delayed, optional)
missing module named nlu - imported by langchain_community.embeddings.johnsnowlabs (delayed, optional)
missing module named johnsnowlabs - imported by langchain_community.embeddings.johnsnowlabs (delayed, optional)
missing module named 'intel_extension_for_transformers.transformers' - imported by langchain_community.embeddings.itrex (delayed)
missing module named 'ipex_llm.transformers' - imported by langchain_community.embeddings.ipex_llm (delayed, optional)
missing module named infinity_emb - imported by langchain_community.embeddings.infinity_local (delayed, optional)
missing module named 'tencentcloud.hunyuan' - imported by langchain_community.embeddings.hunyuan (delayed, optional), langchain_community.chat_models.hunyuan (delayed, optional)
missing module named 'tencentcloud.common' - imported by langchain_community.embeddings.hunyuan (delayed, optional), langchain_community.chat_models.hunyuan (delayed, optional)
missing module named tencentcloud - imported by langchain_community.embeddings.hunyuan (delayed, optional)
missing module named 'elasticsearch.client' - imported by langchain_community.embeddings.elasticsearch (delayed, conditional, optional)
missing module named elasticsearch - imported by langchain_community.chat_message_histories.elasticsearch (delayed, conditional, optional), langchain_community.vectorstores.baiducloud_vector_search (delayed, conditional, optional), langchain_community.vectorstores.ecloud_vector_search (delayed, conditional, optional), langchain_community.vectorstores.elastic_vector_search (delayed, conditional, optional), langchain_community.vectorstores.elasticsearch (delayed, conditional, optional), langchain_community.embeddings.elasticsearch (delayed, conditional, optional), langchain_community.retrievers.elastic_search_bm25 (delayed)
missing module named awadb - imported by langchain_community.vectorstores.awadb (delayed, conditional, optional), langchain_community.embeddings.awa (delayed, optional)
missing module named torch_npu - imported by langchain_community.embeddings.ascend (delayed, optional)
missing module named langsmith_pyo3 - imported by langsmith.client (delayed, conditional, optional)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named 'matplotlib.artist' - imported by pandas._testing.asserters (delayed)
missing module named 'opentelemetry.sdk' - imported by langsmith._internal.otel._otel_client (conditional, optional), langsmith.client (optional), litellm.integrations.opentelemetry (delayed, conditional)
missing module named 'opentelemetry.context' - imported by langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named 'opentelemetry.exporter' - imported by langsmith._internal.otel._otel_client (conditional, optional), litellm.integrations.opentelemetry (delayed)
missing module named 'opentelemetry.trace' - imported by langsmith.client (conditional, optional), langsmith._internal.otel._otel_exporter (conditional, optional), litellm.litellm_core_utils.core_helpers (conditional), litellm.caching.base_cache (conditional), litellm.caching.disk_cache (conditional), litellm.caching.redis_cache (conditional), litellm.proxy._types (conditional), litellm.integrations.custom_logger (conditional), litellm.integrations.opentelemetry (delayed, conditional), litellm.proxy.proxy_server (conditional), litellm.proxy.hooks.parallel_request_limiter (conditional), litellm.proxy.auth.auth_checks (conditional), litellm.proxy.auth.auth_exception_handler (conditional), litellm.proxy.hooks.parallel_request_limiter_v2 (conditional), litellm.proxy.utils (conditional), litellm.router_strategy.lowest_latency (conditional), litellm.router_strategy.lowest_tpm_rpm_v2 (conditional), litellm.router_utils.cooldown_cache (conditional), litellm.router_utils.cooldown_handlers (conditional), litellm.router_utils.handle_error (conditional), litellm.router_utils.prompt_caching_cache (conditional), litellm.router (conditional), litellm.integrations.arize._utils (conditional), litellm.integrations.arize.arize_phoenix (conditional), litellm.integrations.langtrace (conditional), litellm._service_logger (conditional), litellm.caching.dual_cache (conditional), litellm.caching.redis_cluster_cache (conditional), litellm.integrations.arize.arize (conditional), litellm.integrations.traceloop (delayed)
missing module named opentelemetry - imported by langsmith._internal._background_thread (conditional), langsmith.client (conditional, optional), langsmith._internal.otel._otel_exporter (conditional, optional), litellm.integrations.opentelemetry (delayed)
missing module named urlparse - imported by requests_toolbelt._compat (conditional)
missing module named Queue - imported by requests_toolbelt._compat (conditional)
missing module named 'requests.packages.urllib3' - imported by requests_toolbelt._compat (conditional, optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named wolframalpha - imported by langchain_community.utilities.wolfram_alpha (delayed, optional)
missing module named wikipedia - imported by langchain_community.docstore.wikipedia (delayed, optional), langchain_community.utilities.wikipedia (delayed, optional)
missing module named twilio - imported by langchain_community.utilities.twilio (delayed, optional)
missing module named tensorflow_datasets - imported by langchain_community.utilities.tensorflow_datasets (delayed, optional)
missing module named steamspypi - imported by langchain_community.utilities.steam (delayed, optional)
missing module named decouple - imported by langchain_community.utilities.steam (delayed, optional)
missing module named steam - imported by langchain_community.utilities.steam (delayed, optional)
missing module named stackapi - imported by langchain_community.utilities.stackexchange (delayed, optional)
missing module named cnosdb_connector - imported by langchain_community.utilities.sql_database (delayed, optional)
missing module named dbruntime - imported by langchain_community.utilities.sql_database (delayed, optional)
missing module named databricks - imported by langchain_community.utilities.sql_database (delayed, optional)
missing module named 'pyspark.sql' - imported by langchain_community.document_loaders.pyspark_dataframe (delayed, optional), langchain_community.utilities.spark_sql (delayed, conditional, optional)
missing module named serpapi - imported by langchain_community.utilities.google_finance (delayed, optional), langchain_community.utilities.google_jobs (delayed, optional), langchain_community.utilities.google_scholar (delayed, optional), langchain_community.utilities.google_trends (delayed, optional), langchain_community.utilities.serpapi (delayed, optional)
missing module named xmltodict - imported by langchain_community.document_loaders.parsers.vsdx (delayed, optional), langchain_community.utilities.pubmed (delayed, optional)
missing module named 'azure.core' - imported by langchain_community.document_loaders.parsers.doc_intelligence (delayed, conditional), langchain_community.document_loaders.doc_intelligence (conditional), pydantic_settings.sources (delayed, optional), langchain_community.utilities.powerbi (delayed, conditional), langchain_community.tools.azure_ai_services.document_intelligence (delayed, optional), langchain_community.tools.azure_ai_services.image_analysis (delayed, optional), langchain_community.tools.azure_ai_services.text_analytics_for_health (delayed, optional), langchain_community.tools.azure_cognitive_services.form_recognizer (delayed, optional), langchain_community.tools.azure_cognitive_services.text_analytics_health (delayed, optional), langchain_community.vectorstores.azuresearch (delayed, conditional), litellm.proxy.hooks.azure_content_safety (delayed, optional)
missing module named pyowm - imported by langchain_community.utilities.openweathermap (delayed, optional)
missing module named 'riva.client' - imported by langchain_community.utilities.nvidia_riva (delayed, conditional, optional)
missing module named riva - imported by langchain_community.utilities.nvidia_riva (conditional)
missing module named odps - imported by langchain_community.utilities.max_compute (delayed, conditional, optional)
missing module named atlassian - imported by langchain_community.document_loaders.confluence (delayed, optional), langchain_community.utilities.jira (delayed, optional)
missing module named 'gql.transport' - imported by langchain_community.utilities.graphql (delayed, optional)
missing module named gql - imported by langchain_community.utilities.graphql (delayed, optional)
missing module named 'googleapiclient.discovery' - imported by langchain_community.document_loaders.googledrive (delayed), langchain_community.document_loaders.youtube (delayed, optional), langchain_community.utilities.google_search (delayed, optional), langchain_community.tools.gmail.utils (conditional), langchain_community.tools.gmail.base (conditional, optional), langchain_community.agent_toolkits.gmail.toolkit (conditional, optional)
missing module named googlemaps - imported by langchain_community.utilities.google_places_api (delayed, optional)
missing module named duckduckgo_search - imported by langchain_community.utilities.duckduckgo_search (delayed, optional)
missing module named dria - imported by langchain_community.utilities.dria_index (delayed, optional)
missing module named 'dataherald.types' - imported by langchain_community.utilities.dataherald (delayed)
missing module named dataherald - imported by langchain_community.utilities.dataherald (delayed, optional)
missing module named bibtexparser - imported by langchain_community.utilities.bibtex (delayed, optional)
missing module named asknews_sdk - imported by langchain_community.utilities.asknews (delayed, optional), langchain_community.retrievers.asknews (delayed, optional)
missing module named fitz - imported by langchain_community.utilities.arxiv (delayed, optional), langchain_community.document_loaders.bibtex (delayed, optional)
missing module named arxiv - imported by langchain_community.utilities.arxiv (delayed, optional)
missing module named apify_client - imported by langchain_community.document_loaders.apify_dataset (delayed, optional), langchain_community.utilities.apify (delayed, optional)
missing module named pytube - imported by langchain_community.document_loaders.youtube (delayed, optional)
missing module named youtube_transcript_api - imported by langchain_community.document_loaders.youtube (delayed, optional)
missing module named 'google_auth_oauthlib.flow' - imported by langchain_community.document_loaders.youtube (delayed, optional), langchain_community.tools.gmail.utils (conditional)
missing module named 'google.oauth2' - imported by langchain_community.document_loaders.googledrive (delayed, optional), langchain_community.document_loaders.youtube (delayed, optional), langchain_community.tools.gmail.utils (conditional), langchain_community.vectorstores.matching_engine (delayed, conditional, optional), litellm.llms.vertex_ai.vertex_llm_base (delayed), litellm.llms.vertex_ai.vertex_ai_non_gemini (delayed, conditional, optional)
missing module named xorbits - imported by langchain_community.document_loaders.xorbits (delayed, optional)
missing module named 'unstructured.partition' - imported by langchain_community.document_loaders.unstructured (delayed, conditional), langchain_community.document_loaders.csv_loader (delayed), langchain_community.document_loaders.pdf (delayed), langchain_community.document_loaders.chm (delayed), langchain_community.document_loaders.parsers.msword (delayed, optional), langchain_community.document_loaders.email (delayed, conditional), langchain_community.document_loaders.epub (delayed), langchain_community.document_loaders.excel (delayed), langchain_community.document_loaders.html (delayed), langchain_community.document_loaders.image (delayed), langchain_community.document_loaders.lakefs (delayed), langchain_community.document_loaders.markdown (delayed), langchain_community.document_loaders.odt (delayed), langchain_community.document_loaders.org_mode (delayed), langchain_community.document_loaders.powerpoint (delayed, conditional), langchain_community.document_loaders.rst (delayed), langchain_community.document_loaders.rtf (delayed), langchain_community.document_loaders.s3_file (delayed), langchain_community.document_loaders.tsv (delayed), langchain_community.document_loaders.url (delayed), langchain_community.document_loaders.url_playwright (delayed), langchain_community.document_loaders.url_selenium (delayed), langchain_community.document_loaders.word_document (delayed, conditional), langchain_community.document_loaders.xml (delayed)
missing module named magic - imported by langchain_community.document_loaders.powerpoint (delayed, optional), langchain_community.document_loaders.word_document (delayed, optional)
missing module named 'unstructured.file_utils' - imported by langchain_community.document_loaders.email (delayed), langchain_community.document_loaders.powerpoint (delayed), langchain_community.document_loaders.word_document (delayed)
missing module named docx2txt - imported by langchain_community.document_loaders.confluence (delayed, optional), langchain_community.document_loaders.word_document (delayed)
missing module named fake_useragent - imported by langchain_community.document_loaders.async_html (delayed, conditional, optional), langchain_community.document_loaders.web_base (delayed, conditional, optional)
missing module named 'textractor.entities' - imported by langchain_community.document_loaders.parsers.pdf (delayed, optional)
missing module named textractcaller - imported by langchain_community.document_loaders.parsers.pdf (delayed, optional), langchain_community.document_loaders.pdf (delayed, optional)
missing module named 'pypdfium2.raw' - imported by langchain_community.document_loaders.parsers.pdf (delayed)
missing module named 'pymupdf.table' - imported by langchain_community.document_loaders.parsers.pdf (delayed, conditional, optional)
missing module named 'pdfminer.image' - imported by langchain_community.document_loaders.parsers.pdf (delayed, conditional)
missing module named 'pdfminer.pdfinterp' - imported by langchain_community.document_loaders.parsers.pdf (delayed, optional)
missing module named 'pdfminer.layout' - imported by langchain_community.document_loaders.parsers.pdf (delayed, optional), langchain_community.document_loaders.pdf (delayed)
missing module named 'pdfminer.converter' - imported by langchain_community.document_loaders.parsers.pdf (delayed, optional)
missing module named 'pdfminer.pdfpage' - imported by langchain_community.document_loaders.parsers.pdf (delayed, optional)
missing module named 'pdfminer.psparser' - imported by langchain_community.document_loaders.parsers.pdf (delayed)
missing module named pdfminer - imported by langchain_community.document_loaders.parsers.pdf (delayed, optional)
missing module named rapidocr_onnxruntime - imported by langchain_community.document_loaders.parsers.images (delayed, conditional, optional), langchain_community.document_loaders.parsers.pdf (delayed, optional)
missing module named textractor - imported by langchain_community.document_loaders.parsers.pdf (conditional)
missing module named pypdfium2 - imported by langchain_community.document_loaders.parsers.pdf (delayed, conditional, optional)
missing module named 'Crypto.Util' - imported by pypdf._crypt_providers._pycryptodome (top-level)
missing module named 'Crypto.Cipher' - imported by pypdf._crypt_providers._pycryptodome (top-level)
missing module named Crypto - imported by pypdf._crypt_providers._pycryptodome (top-level)
missing module named pymupdf - imported by langchain_community.document_loaders.parsers.pdf (delayed, conditional, optional)
missing module named pdfplumber - imported by langchain_community.document_loaders.parsers.pdf (delayed, conditional), langchain_community.document_loaders.pdf (delayed, optional)
missing module named tree_sitter_languages - imported by langchain_community.document_loaders.parsers.language.tree_sitter_segmenter (delayed, optional), langchain_community.document_loaders.parsers.language.c (delayed), langchain_community.document_loaders.parsers.language.cpp (delayed), langchain_community.document_loaders.parsers.language.csharp (delayed), langchain_community.document_loaders.parsers.language.elixir (delayed), langchain_community.document_loaders.parsers.language.go (delayed), langchain_community.document_loaders.parsers.language.java (delayed), langchain_community.document_loaders.parsers.language.kotlin (delayed), langchain_community.document_loaders.parsers.language.lua (delayed), langchain_community.document_loaders.parsers.language.perl (delayed), langchain_community.document_loaders.parsers.language.php (delayed), langchain_community.document_loaders.parsers.language.ruby (delayed), langchain_community.document_loaders.parsers.language.rust (delayed), langchain_community.document_loaders.parsers.language.scala (delayed), langchain_community.document_loaders.parsers.language.sql (delayed), langchain_community.document_loaders.parsers.language.typescript (delayed)
missing module named tree_sitter - imported by langchain_community.document_loaders.parsers.language.tree_sitter_segmenter (delayed, conditional, optional), langchain_community.document_loaders.parsers.language.c (conditional), langchain_community.document_loaders.parsers.language.cpp (conditional), langchain_community.document_loaders.parsers.language.csharp (conditional), langchain_community.document_loaders.parsers.language.elixir (conditional), langchain_community.document_loaders.parsers.language.go (conditional), langchain_community.document_loaders.parsers.language.java (conditional), langchain_community.document_loaders.parsers.language.kotlin (conditional), langchain_community.document_loaders.parsers.language.lua (conditional), langchain_community.document_loaders.parsers.language.perl (conditional), langchain_community.document_loaders.parsers.language.php (conditional), langchain_community.document_loaders.parsers.language.ruby (conditional), langchain_community.document_loaders.parsers.language.rust (conditional), langchain_community.document_loaders.parsers.language.scala (conditional), langchain_community.document_loaders.parsers.language.sql (conditional), langchain_community.document_loaders.parsers.language.typescript (conditional)
missing module named esprima - imported by langchain_community.document_loaders.parsers.language.javascript (delayed, optional)
missing module named 'google.longrunning' - imported by langchain_community.document_loaders.parsers.docai (delayed, optional)
missing module named 'azure.ai' - imported by langchain_community.document_loaders.parsers.doc_intelligence (delayed), langchain_community.tools.azure_ai_services.document_intelligence (delayed, optional), langchain_community.tools.azure_ai_services.image_analysis (delayed, optional), langchain_community.tools.azure_ai_services.text_analytics_for_health (delayed, optional), langchain_community.tools.azure_cognitive_services.form_recognizer (delayed, optional), langchain_community.tools.azure_cognitive_services.image_analysis (delayed, optional), langchain_community.tools.azure_cognitive_services.text_analytics_health (delayed, optional), litellm.proxy.hooks.azure_content_safety (delayed, optional)
missing module named pyaudioop - imported by pydub.utils (optional)
missing module named audioop - imported by pydub.utils (optional)
missing module named faster_whisper - imported by langchain_community.document_loaders.parsers.audio (delayed, optional)
missing module named 'speechkit.stt' - imported by langchain_community.document_loaders.parsers.audio (delayed, optional)
missing module named speechkit - imported by langchain_community.document_loaders.parsers.audio (delayed, optional)
missing module named librosa - imported by langchain_community.document_loaders.parsers.audio (delayed, optional)
missing module named 'selenium.common' - imported by langchain_community.document_loaders.url_selenium (delayed)
missing module named 'selenium.webdriver' - imported by langchain_community.document_loaders.url_selenium (delayed, conditional)
missing module named unstructured - imported by langchain_community.document_loaders.unstructured (delayed, optional), langchain_community.document_loaders.url (delayed, optional), langchain_community.document_loaders.url_playwright (delayed, optional), langchain_community.document_loaders.url_selenium (delayed, optional)
missing module named selenium - imported by langchain_community.document_loaders.url_selenium (delayed, conditional, optional)
missing module named 'unstructured.__version__' - imported by langchain_community.document_loaders.url (delayed, optional)
missing module named tweepy - imported by langchain_community.document_loaders.twitter (delayed, conditional, optional)
missing module named trello - imported by langchain_community.document_loaders.trello (delayed, conditional, optional)
missing module named chardet - imported by requests (optional), langchain_community.document_loaders.helpers (delayed)
missing module named qcloud_cos - imported by langchain_community.document_loaders.tencent_cos_file (delayed, optional), langchain_community.document_loaders.tencent_cos_directory (delayed, optional)
missing module named nest_asyncio - imported by langchain_community.document_loaders.telegram (delayed, conditional, optional)
missing module named 'telethon.sync' - imported by langchain_community.document_loaders.telegram (delayed)
missing module named telethon - imported by langchain_community.document_loaders.telegram (conditional)
missing module named surrealdb - imported by langchain_community.document_loaders.surrealdb (delayed, optional), langchain_community.vectorstores.surrealdb (delayed, optional)
missing module named pysrt - imported by langchain_community.document_loaders.srt (delayed, optional)
missing module named spider - imported by langchain_community.document_loaders.spider (delayed, optional)
missing module named snowflake - imported by langchain_community.document_loaders.snowflake_loader (delayed, optional)
missing module named 'O365.drive' - imported by langchain_community.document_loaders.base_o365 (conditional), langchain_community.document_loaders.sharepoint (delayed, optional)
missing module named O365 - imported by langchain_community.document_loaders.onedrive_file (conditional), langchain_community.document_loaders.base_o365 (delayed, conditional, optional), langchain_community.tools.office365.utils (delayed, conditional, optional), langchain_community.tools.office365.base (conditional), langchain_community.agent_toolkits.office365.toolkit (conditional)
missing module named 'azure.keyvault' - imported by pydantic_settings.sources (delayed, optional), litellm.proxy.proxy_server (delayed, optional)
missing module named scrapingant_client - imported by langchain_community.document_loaders.scrapingant (delayed, optional)
missing module named scrapfly - imported by langchain_community.document_loaders.scrapfly (delayed, optional)
missing module named feedparser - imported by langchain_community.document_loaders.rss (delayed, optional)
missing module named listparser - imported by langchain_community.document_loaders.rss (delayed, optional)
missing module named 'rockset.models' - imported by langchain_community.document_loaders.rocksetdb (delayed, optional), langchain_community.vectorstores.rocksetdb (delayed, optional)
missing module named rockset - imported by langchain_community.document_loaders.rocksetdb (delayed, optional), langchain_community.chat_message_histories.rocksetdb (delayed, optional), langchain_community.vectorstores.rocksetdb (delayed, optional)
missing module named praw - imported by langchain_community.document_loaders.reddit (delayed, conditional, optional), langchain_community.utilities.reddit_search (delayed, optional)
missing module named pyspark - imported by langchain_community.document_loaders.pyspark_dataframe (conditional)
missing module named psychicapi - imported by langchain_community.document_loaders.psychic (delayed, optional)
missing module named polars - imported by langchain_community.document_loaders.polars_dataframe (delayed), streamlit.dataframe_util (delayed, conditional), streamlit.runtime.caching.hashing (delayed, conditional)
missing module named pyzerox - imported by langchain_community.document_loaders.pdf (delayed)
missing module named 'dedoc.utils' - imported by langchain_community.document_loaders.dedoc (delayed), langchain_community.document_loaders.pdf (delayed)
missing module named 'pdfminer.utils' - imported by langchain_community.document_loaders.pdf (delayed)
missing module named 'pdfminer.high_level' - imported by langchain_community.document_loaders.pdf (delayed, optional)
missing module named 'textractor.data' - imported by langchain_community.document_loaders.pdf (conditional)
missing module named sodapy - imported by langchain_community.document_loaders.open_city_data (delayed)
missing module named obs - imported by langchain_community.document_loaders.obs_file (delayed, optional), langchain_community.document_loaders.obs_directory (delayed, optional)
missing module named newspaper - imported by langchain_community.document_loaders.news (delayed, optional)
missing module named 'needle.v1' - imported by langchain_community.document_loaders.needle (delayed, optional), langchain_community.retrievers.needle (delayed, optional)
missing module named needle - imported by langchain_community.document_loaders.needle (delayed, optional)
missing module named motor - imported by langchain_community.document_loaders.mongodb (delayed, optional)
missing module named mwparserfromhell - imported by langchain_community.document_loaders.mediawikidump (delayed, optional)
missing module named mwxml - imported by langchain_community.document_loaders.mediawikidump (delayed, optional)
missing module named mastodon - imported by langchain_community.document_loaders.mastodon (delayed, conditional, optional)
missing module named 'llmsherpa.readers' - imported by langchain_community.document_loaders.llmsherpa (delayed)
missing module named llmsherpa - imported by langchain_community.document_loaders.llmsherpa (delayed, optional)
missing module named gpudb - imported by langchain_community.document_loaders.kinetica_loader (delayed, optional), langchain_community.vectorstores.kinetica (delayed, optional), langchain_community.chat_models.kinetica (delayed, conditional, optional)
missing module named jq - imported by langchain_community.document_loaders.json_loader (delayed, optional)
missing module named PyPDF2 - imported by langchain_community.document_loaders.googledrive (delayed, conditional)
missing module named 'googleapiclient.http' - imported by langchain_community.document_loaders.googledrive (delayed)
missing module named 'googleapiclient.errors' - imported by langchain_community.document_loaders.googledrive (delayed)
missing module named googleapiclient - imported by langchain_community.document_loaders.googledrive (delayed)
missing module named google_auth_oauthlib - imported by langchain_community.document_loaders.googledrive (delayed, optional)
missing module named 'boto3.session' - imported by langchain_community.document_loaders.glue_catalog (conditional), langchain_community.chat_message_histories.dynamodb (conditional)
missing module named gitdb_speedups - imported by gitdb.fun (optional)
missing module named 'gitdb_speedups._perf' - imported by gitdb.stream (optional), gitdb.pack (optional)
missing module named sha - imported by gitdb.util (delayed, optional)
missing module named geopandas - imported by langchain_community.document_loaders.geodataframe (delayed, optional)
missing module named firecrawl - imported by langchain_community.document_loaders.firecrawl (delayed, optional)
missing module named 'fauna.encoding' - imported by langchain_community.document_loaders.fauna (delayed, optional)
missing module named 'fauna.client' - imported by langchain_community.document_loaders.fauna (delayed, optional)
missing module named fauna - imported by langchain_community.document_loaders.fauna (delayed, optional)
missing module named html2text - imported by langchain_community.document_loaders.evernote (delayed, optional), langchain_community.document_transformers.html2text (delayed, optional)
missing module named extract_msg - imported by langchain_community.document_loaders.email (delayed, optional)
missing module named duckdb - imported by langchain_community.document_loaders.duckdb_loader (delayed, optional), langchain_community.vectorstores.duckdb (delayed, optional)
missing module named 'dropbox.files' - imported by langchain_community.document_loaders.dropbox (delayed, optional)
missing module named dropbox - imported by langchain_community.document_loaders.dropbox (delayed, optional)
missing module named 'dgml_utils.segmentation' - imported by langchain_community.document_loaders.docugami (delayed, optional)
missing module named dgml_utils - imported by langchain_community.document_loaders.docugami (delayed, optional)
missing module named dedoc - imported by langchain_community.document_loaders.dedoc (delayed, optional)
missing module named modin - imported by langchain_community.document_loaders.dataframe (delayed, conditional, optional)
missing module named 'datadog_api_client.v2' - imported by langchain_community.document_loaders.datadog_logs (delayed, optional)
missing module named datadog_api_client - imported by langchain_community.document_loaders.datadog_logs (delayed, optional)
missing module named 'couchbase.options' - imported by langchain_community.document_loaders.couchbase (delayed, optional), langchain_community.vectorstores.couchbase (delayed)
missing module named 'couchbase.cluster' - imported by langchain_community.document_loaders.couchbase (delayed, optional), langchain_community.vectorstores.couchbase (delayed, conditional, optional)
missing module named couchbase - imported by langchain_community.document_loaders.couchbase (delayed, optional)
missing module named svglib - imported by langchain_community.document_loaders.confluence (delayed, optional)
missing module named reportlab - imported by langchain_community.document_loaders.confluence (delayed, optional)
missing module named pdf2image - imported by langchain_community.document_loaders.confluence (delayed, optional)
missing module named markdownify - imported by langchain_community.document_loaders.confluence (delayed, conditional, optional), langchain_community.document_transformers.markdownify (delayed, optional)
missing module named chm - imported by langchain_community.document_loaders.chm (delayed, conditional)
missing module named cassio - imported by langchain_community.document_loaders.cassandra (delayed, conditional, optional)
missing module named 'cassandra.query' - imported by langchain_community.document_loaders.cassandra (conditional), langchain_community.storage.cassandra (delayed, conditional)
missing module named 'cassandra.pool' - imported by langchain_community.document_loaders.cassandra (conditional)
missing module named 'cassandra.cluster' - imported by langchain_community.document_loaders.cassandra (conditional), langchain_community.utilities.cassandra_database (conditional), langchain_community.tools.cassandra_database.tool (conditional), langchain_community.chat_message_histories.cassandra (conditional), langchain_community.vectorstores.cassandra (conditional), langchain_community.storage.cassandra (conditional)
missing module named cassandra - imported by langchain_community.utilities.cassandra (conditional)
missing module named yt_dlp - imported by langchain_community.document_loaders.blob_loaders.youtube_audio (delayed, optional)
missing module named cloudpathlib - imported by langchain_community.document_loaders.blob_loaders.cloud_blob_loader (delayed, conditional)
missing module named bilibili_api - imported by langchain_community.document_loaders.bilibili (delayed, optional)
missing module named azure - imported by langchain_community.document_loaders.azure_blob_storage_file (delayed, optional)
missing module named 'azure.storage' - imported by langchain_community.document_loaders.azure_blob_storage_container (delayed, optional), litellm.integrations.azure_storage.azure_storage (delayed)
missing module named azureml - imported by langchain_community.document_loaders.azure_ai_data (delayed, optional)
missing module named 'astrapy.db' - imported by langchain_community.utilities.astradb (delayed, optional), langchain_community.document_loaders.astradb (conditional), langchain_community.chat_message_histories.astradb (conditional), langchain_community.vectorstores.astradb (conditional), langchain_community.storage.astradb (conditional)
missing module named astrapy - imported by langchain_community.utilities.astradb (conditional)
missing module named assemblyai - imported by langchain_community.document_loaders.assemblyai (delayed, conditional, optional)
missing module named arcgis - imported by langchain_community.document_loaders.arcgis_loader (delayed, conditional, optional)
missing module named pyairtable - imported by langchain_community.document_loaders.airtable (delayed)
missing module named 'airbyte_cdk.sources' - imported by langchain_community.document_loaders.airbyte (delayed)
missing module named airbyte_cdk - imported by langchain_community.document_loaders.airbyte (delayed)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional), langchain.utilities.asyncio (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level), uvicorn.workers (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named youtube_search - imported by langchain_community.tools.youtube.search (delayed)
missing module named yfinance - imported by langchain_community.tools.yahoo_finance_news (delayed, optional)
missing module named steamship - imported by langchain_community.tools.steamship_image_generation.utils (conditional), langchain_community.tools.steamship_image_generation.tool (delayed, conditional, optional)
missing module named 'steamship.utils' - imported by langchain_community.tools.steamship_image_generation.utils (delayed, optional)
missing module named 'steamship.data' - imported by langchain_community.tools.steamship_image_generation.utils (delayed, optional)
missing module named slack_sdk - imported by langchain_community.tools.slack.utils (delayed, conditional, optional), langchain_community.tools.slack.base (conditional, optional), langchain_community.agent_toolkits.slack.toolkit (conditional, optional)
missing module named langchain_experimental - imported by langchain_community.tools.shell.tool (delayed, optional)
missing module named openapi_pydantic - imported by langchain_community.utilities.openapi (delayed, conditional, optional), langchain_community.tools.openapi.utils.api_models (delayed, conditional)
missing module named elevenlabs - imported by langchain_community.tools.eleven_labs.text2speech (delayed, optional)
missing module named 'e2b.templates' - imported by langchain_community.tools.e2b_data_analysis.tool (conditional)
missing module named e2b - imported by langchain_community.tools.e2b_data_analysis.tool (delayed, conditional, optional)
missing module named 'cassio.config' - imported by langchain_community.utilities.cassandra_database (delayed, optional), langchain_community.storage.cassandra (delayed, conditional, optional)
missing module named 'azure.cognitiveservices' - imported by langchain_community.tools.azure_ai_services.speech_to_text (delayed, optional), langchain_community.tools.azure_ai_services.text_to_speech (delayed, optional), langchain_community.tools.azure_cognitive_services.speech2text (delayed, optional), langchain_community.tools.azure_cognitive_services.text2speech (delayed, optional)
missing module named 'ain.types' - imported by langchain_community.tools.ainetwork.app (delayed), langchain_community.tools.ainetwork.owner (delayed), langchain_community.tools.ainetwork.rule (delayed), langchain_community.tools.ainetwork.value (delayed)
missing module named 'ain.ain' - imported by langchain_community.tools.ainetwork.utils (delayed, optional), langchain_community.tools.ainetwork.base (conditional), langchain_community.agent_toolkits.ainetwork.toolkit (conditional)
missing module named ain - imported by langchain_community.tools.ainetwork.utils (conditional)
missing module named 'ain.utils' - imported by langchain_community.tools.ainetwork.app (delayed)
missing module named jwt - imported by litellm.proxy.auth.handle_jwt (delayed), litellm.proxy.management_endpoints.ui_sso (delayed), litellm.proxy.proxy_server (delayed, conditional), langchain_community.chat_models.zhipuai (delayed, optional)
missing module named 'snowflake.snowpark' - imported by streamlit.connections.util (delayed, optional), streamlit.connections.snowflake_connection (delayed, conditional), streamlit.connections.snowpark_connection (delayed, conditional), langchain_community.chat_models.snowflake (delayed, optional)
missing module named reka - imported by langchain_community.chat_models.reka (delayed, optional)
missing module named 'premai.api' - imported by langchain_community.chat_models.premai (conditional)
missing module named 'outlines.models' - imported by langchain_community.chat_models.outlines (delayed, optional)
missing module named 'mlx.core' - imported by langchain_community.chat_models.mlx (delayed, optional)
missing module named ddtrace - imported by litellm.litellm_core_utils.dd_tracing (conditional, optional), litellm.proxy.proxy_server (delayed, conditional)
missing module named 'litellm_enterprise.enterprise_callbacks' - imported by litellm.proxy.common_utils.callback_utils (delayed, conditional, optional), litellm.proxy.hooks.key_management_event_hooks (delayed, optional), litellm.proxy.guardrails.guardrail_initializers (delayed, optional), litellm.proxy.hooks.user_management_event_hooks (delayed, optional), litellm.litellm_core_utils.litellm_logging (optional)
missing module named litellm.get_secret - imported by litellm (top-level), litellm.proxy.common_utils.callback_utils (top-level), litellm.proxy.guardrails.guardrail_hooks.presidio (top-level), litellm.integrations.prometheus_helpers.prometheus_api (top-level), litellm._redis (top-level)
missing module named 'botocore.awsrequest' - imported by langchain_community.graphs.neptune_rdf_graph (delayed, conditional), litellm.types.llms.bedrock (conditional), litellm.llms.bedrock.base_aws_llm (delayed, conditional, optional), litellm.llms.bedrock.chat.invoke_handler (delayed, optional), litellm.integrations.vector_stores.bedrock_vector_store (delayed, optional), litellm.proxy.pass_through_endpoints.llm_passthrough_endpoints (delayed, optional), litellm.llms.bedrock.embed.embedding (delayed, optional), litellm.llms.bedrock.image.image_handler (delayed, conditional, optional), litellm.llms.sagemaker.chat.handler (delayed, optional), litellm.llms.sagemaker.completion.handler (delayed, optional), litellm.proxy.guardrails.guardrail_hooks.bedrock_guardrails (delayed, optional), litellm.secret_managers.aws_secret_manager_v2 (delayed, optional), litellm.llms.bedrock.rerank.handler (delayed, conditional, optional)
missing module named 'botocore.auth' - imported by langchain_community.graphs.neptune_rdf_graph (delayed, conditional), litellm.llms.bedrock.base_aws_llm (delayed, optional), litellm.llms.bedrock.chat.invoke_handler (delayed, optional), litellm.integrations.vector_stores.bedrock_vector_store (delayed, optional), litellm.proxy.pass_through_endpoints.llm_passthrough_endpoints (delayed, optional), litellm.llms.bedrock.embed.embedding (delayed, optional), litellm.llms.bedrock.image.image_handler (delayed, optional), litellm.llms.sagemaker.chat.handler (delayed, optional), litellm.llms.sagemaker.completion.handler (delayed, optional), litellm.proxy.guardrails.guardrail_hooks.bedrock_guardrails (delayed, optional), litellm.secret_managers.aws_secret_manager_v2 (delayed, optional), litellm.llms.bedrock.rerank.handler (delayed, optional)
missing module named 'botocore.credentials' - imported by litellm.llms.bedrock.base_aws_llm (delayed, conditional, optional), litellm.llms.bedrock.chat.invoke_handler (delayed, optional), litellm.proxy.pass_through_endpoints.llm_passthrough_endpoints (delayed, optional), litellm.proxy.common_utils.load_config_utils (delayed, optional), litellm.llms.bedrock.embed.embedding (delayed, optional), litellm.llms.sagemaker.chat.handler (delayed, optional), litellm.llms.sagemaker.completion.handler (delayed, optional), litellm.proxy.guardrails.guardrail_hooks.bedrock_guardrails (delayed, optional)
missing module named litellm.vertex_chat_completion - imported by litellm (delayed), litellm.integrations.gcs_bucket.gcs_bucket_base (delayed), litellm.integrations.gcs_pubsub.pub_sub (delayed)
missing module named 'mcp.types' - imported by litellm.proxy._experimental.mcp_server.server (conditional), litellm.proxy._experimental.mcp_server.mcp_server_manager (top-level), litellm.proxy._experimental.mcp_server.sse_transport (top-level)
missing module named prometheus_client - imported by litellm.integrations.prometheus (delayed, optional), litellm.integrations.prometheus_services (delayed, optional)
missing module named 'apscheduler.schedulers' - imported by litellm.integrations.prometheus (conditional)
missing module named 'azure.identity' - imported by langchain_community.vectorstores.azure_cosmos_db_no_sql (conditional), langchain_community.vectorstores.azuresearch (delayed), litellm.secret_managers.get_azure_ad_token_provider (delayed), litellm.llms.azure.common_utils (delayed), litellm.proxy.proxy_server (delayed, optional)
missing module named 'litellm_enterprise.proxy' - imported by litellm.proxy.auth.user_api_key_auth (optional), litellm.proxy.proxy_server (optional)
missing module named 'litellm.proxy.enterprise' - imported by litellm.proxy.proxy_server (optional)
missing module named litellm.print_verbose - imported by litellm (top-level), litellm.scheduler (top-level)
missing module named litellm.get_secret_str - imported by litellm (top-level), litellm.files.main (top-level), litellm.router (top-level), litellm.proxy.openai_files_endpoints.files_endpoints (top-level), litellm.proxy.common_utils.debug_utils (top-level), litellm._redis (top-level), litellm.proxy.proxy_cli (delayed, conditional)
missing module named prisma - imported by litellm.proxy.db.log_db_metrics (delayed), litellm.proxy.db.prisma_client (delayed), litellm.proxy.db.exception_handler (delayed), litellm.proxy.utils (delayed, optional)
missing module named 'enterprise.enterprise_hooks' - imported by litellm.proxy.common_utils.callback_utils (delayed, conditional, optional), litellm.proxy.hooks (optional), litellm.proxy.management_endpoints.customer_endpoints (delayed, optional)
missing module named litellm_proxy_extras - imported by litellm.proxy.db.prisma_client (delayed, conditional, optional)
missing module named 'enterprise.utils' - imported by litellm.proxy.spend_tracking.spend_management_endpoints (delayed, optional)
missing module named litellm.proxy.pass_through_endpoints.types - imported by litellm.proxy.pass_through_endpoints.llm_provider_handlers.base_passthrough_logging_handler (conditional), litellm.proxy.pass_through_endpoints.llm_provider_handlers.vertex_passthrough_logging_handler (conditional), litellm.proxy.pass_through_endpoints.llm_provider_handlers.anthropic_passthrough_logging_handler (conditional)
missing module named litellm.stream_chunk_builder - imported by litellm (top-level), litellm.proxy.pass_through_endpoints.llm_provider_handlers.cohere_passthrough_logging_handler (top-level)
missing module named 'botocore.eventstream' - imported by litellm.llms.bedrock.chat.invoke_handler (delayed), litellm.llms.sagemaker.common_utils (delayed)
missing module named 'botocore.parsers' - imported by litellm.llms.bedrock.chat.invoke_handler (delayed), litellm.llms.sagemaker.common_utils (delayed)
missing module named 'botocore.model' - imported by litellm.llms.bedrock.chat.invoke_handler (delayed, conditional), litellm.llms.sagemaker.common_utils (delayed, conditional)
missing module named 'botocore.loaders' - imported by litellm.llms.bedrock.chat.invoke_handler (delayed, conditional), litellm.llms.sagemaker.common_utils (delayed, conditional)
missing module named litellm.CreateFileRequest - imported by litellm (top-level), litellm.proxy.openai_files_endpoints.files_endpoints (top-level)
missing module named 'fastapi_sso.sso' - imported by litellm.proxy.management_endpoints.ui_sso (delayed, conditional)
missing module named fastapi_sso - imported by litellm.proxy.management_endpoints.types (top-level)
missing module named 'litellm_enterprise.types' - imported by litellm.proxy.hooks.key_management_event_hooks (delayed, optional)
missing module named 'langfuse.model' - imported by litellm.integrations.langfuse.langfuse (delayed)
missing module named langfuse - imported by litellm.integrations.langfuse.langfuse (delayed, conditional, optional), litellm.integrations.langfuse.langfuse_prompt_management (delayed, conditional, optional)
missing module named 'databricks.sdk' - imported by litellm.llms.databricks.common_utils (delayed, conditional, optional)
missing module named 'nacl.secret' - imported by litellm.proxy.common_utils.encrypt_decrypt_utils (delayed)
missing module named 'nacl.utils' - imported by litellm.proxy.common_utils.encrypt_decrypt_utils (delayed)
missing module named nacl - imported by litellm.proxy.common_utils.encrypt_decrypt_utils (delayed)
missing module named objgraph - imported by litellm.proxy.common_utils.debug_utils (conditional, optional)
missing module named litellm_enterprise - imported by litellm.proxy.common_utils.callback_utils (delayed, conditional, optional)
missing module named 'jwt.algorithms' - imported by litellm.proxy.auth.handle_jwt (delayed)
missing module named sse_starlette - imported by litellm.proxy._experimental.mcp_server.sse_transport (top-level)
missing module named 'mcp.server' - imported by litellm.proxy._experimental.mcp_server.server (conditional, optional)
missing module named apscheduler - imported by litellm.proxy.proxy_server (optional)
missing module named backoff - imported by litellm.proxy.proxy_server (optional), litellm.proxy.utils (optional)
missing module named litellm.integrations.arize.opentelemetry - imported by litellm.integrations.arize.arize_phoenix (conditional)
missing module named 'redisvl.utils' - imported by litellm.caching.redis_semantic_cache (delayed)
missing module named redisvl - imported by litellm.caching.redis_semantic_cache (delayed)
missing module named 'redis.cluster' - imported by langchain_community.utilities.redis (delayed), litellm._redis (delayed, conditional)
missing module named redis - imported by langchain.memory.entity (delayed, optional), langchain_community.utilities.redis (delayed, optional), langchain_community.graphs.falkordb_graph (delayed), langchain_community.chat_message_histories.redis (delayed, optional), langchain_community.vectorstores.redis.base (delayed, optional), langchain_community.storage.redis (delayed, optional), litellm._redis (top-level)
missing module named 'redis.asyncio' - imported by litellm.caching.redis_cache (delayed, conditional), litellm._redis (top-level), litellm.caching.redis_cluster_cache (delayed, conditional)
missing module named diskcache - imported by litellm.caching.disk_cache (delayed)
missing module named enterprise - imported by litellm.responses.litellm_completion_transformation.transformation (optional), litellm.proxy.proxy_server (optional)
missing module named numpydoc - imported by litellm.utils (delayed, optional)
missing module named slack_bolt - imported by litellm.litellm_core_utils.litellm_logging (delayed, conditional, optional)
missing module named posthog - imported by litellm.litellm_core_utils.litellm_logging (delayed, conditional, optional)
missing module named sentry_sdk - imported by litellm.litellm_core_utils.litellm_logging (delayed, conditional, optional)
missing module named 'litellm_enterprise.litellm_core_utils' - imported by litellm.litellm_core_utils.litellm_logging (optional)
missing module named wandb - imported by litellm.integrations.weights_biases (delayed, optional)
missing module named 'opentelemetry.semconv' - imported by litellm.integrations.traceloop (delayed)
missing module named 'traceloop.sdk' - imported by litellm.integrations.traceloop (delayed, optional)
missing module named traceloop - imported by litellm.integrations.traceloop (delayed, optional)
missing module named supabase - imported by langchain_community.vectorstores.supabase (delayed, conditional, optional), litellm.integrations.supabase (delayed, optional)
missing module named lunary - imported by litellm.integrations.lunary (delayed, optional)
missing module named logfire - imported by litellm.integrations.logfire_logger (delayed, optional)
missing module named 'langfuse.client' - imported by litellm.integrations.langfuse.langfuse_prompt_management (conditional)
missing module named 'mlflow.tracing' - imported by litellm.integrations.mlflow (delayed, optional)
missing module named 'mlflow.entities' - imported by litellm.integrations.mlflow (delayed)
missing module named 'mlflow.tracking' - imported by litellm.integrations.mlflow (delayed)
missing module named proto - imported by litellm.litellm_core_utils.streaming_handler (delayed, conditional, optional)
missing module named 'prometheus_client.gc_collector' - imported by litellm.integrations.prometheus_services (delayed, optional)
missing module named 'gunicorn.arbiter' - imported by uvicorn.workers (top-level)
missing module named watchgod - imported by uvicorn.supervisors.watchgodreload (top-level)
missing module named watchfiles - imported by uvicorn.supervisors.watchfilesreload (top-level)
missing module named 'wsproto.utilities' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named 'wsproto.extensions' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named 'wsproto.connection' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named wsproto - imported by uvicorn.protocols.websockets.wsproto_impl (top-level), uvicorn.protocols.websockets.auto (optional)
missing module named httptools - imported by uvicorn.protocols.http.httptools_impl (top-level), uvicorn.protocols.http.auto (optional)
missing module named a2wsgi - imported by uvicorn.middleware.wsgi (optional)
missing module named proxy_server - imported by litellm.proxy.proxy_cli (delayed, conditional, optional)
missing module named 'gunicorn.app' - imported by litellm.proxy.proxy_cli (delayed, conditional)
missing module named 'hypercorn.config' - imported by litellm.proxy.proxy_cli (delayed)
missing module named hypercorn - imported by litellm.proxy.proxy_cli (delayed)
missing module named 'gpt_router.client' - imported by langchain_community.chat_models.gpt_router (delayed, optional)
missing module named 'gpt_router.models' - imported by langchain_community.chat_models.gpt_router (delayed)
missing module named gpt_router - imported by langchain_community.chat_models.gpt_router (delayed, conditional)
missing module named 'Xlib.XK' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named 'Xlib.ext' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named Xlib - imported by mouseinfo (conditional), pyautogui._pyautogui_x11 (top-level)
missing module named 'Xlib.display' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named Tkinter - imported by pymsgbox (conditional, optional), mouseinfo (conditional, optional)
missing module named 'rubicon.objc' - imported by mouseinfo (conditional)
missing module named rubicon - imported by mouseinfo (conditional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named pymilvus - imported by langchain_community.vectorstores.milvus (delayed, optional), langchain_community.vectorstores.zilliz (delayed)
missing module named 'zep_cloud.client' - imported by langchain_community.chat_message_histories.zep_cloud (delayed, optional), langchain_community.retrievers.zep_cloud (delayed, conditional, optional), langchain_community.vectorstores.zep_cloud (delayed, optional)
missing module named zep_cloud - imported by langchain_community.chat_message_histories.zep_cloud (delayed, conditional), langchain_community.retrievers.zep_cloud (conditional), langchain_community.vectorstores.zep_cloud (delayed, conditional)
missing module named zep_python - imported by langchain_community.chat_message_histories.zep (delayed, conditional, optional), langchain_community.retrievers.zep (delayed, optional), langchain_community.vectorstores.zep (delayed, optional)
missing module named 'zep_python.document' - imported by langchain_community.vectorstores.zep (delayed, conditional)
missing module named 'psycopg2.extras' - imported by langchain_community.vectorstores.yellowbrick (delayed)
missing module named 'psycopg2.extensions' - imported by langchain_community.vectorstores.yellowbrick (conditional)
missing module named 'weaviate.util' - imported by langchain_community.retrievers.weaviate_hybrid_search (delayed), langchain_community.vectorstores.weaviate (delayed, optional)
missing module named weaviate - imported by langchain_community.retrievers.weaviate_hybrid_search (delayed, optional), langchain_community.vectorstores.weaviate (delayed, conditional, optional)
missing module named 'vlite.utils' - imported by langchain_community.vectorstores.vlite (delayed, conditional, optional)
missing module named vlite - imported by langchain_community.vectorstores.vlite (delayed, optional)
missing module named 'vespa.application' - imported by langchain_community.vectorstores.vespa (delayed, optional)
missing module named vearch_cluster - imported by langchain_community.vectorstores.vearch (delayed, conditional, optional)
missing module named vearch - imported by langchain_community.vectorstores.vearch (delayed, conditional, optional)
missing module named vdms - imported by langchain_community.vectorstores.vdms (delayed, conditional, optional)
missing module named 'vald.v1' - imported by langchain_community.vectorstores.vald (delayed, optional)
missing module named vald - imported by langchain_community.vectorstores.vald (delayed, optional)
missing module named 'upstash_vector.types' - imported by langchain_community.vectorstores.upstash (conditional)
missing module named upstash_vector - imported by langchain_community.vectorstores.upstash (delayed, conditional, optional)
missing module named 'typesense.exceptions' - imported by langchain_community.vectorstores.typesense (delayed)
missing module named 'typesense.collection' - imported by langchain_community.vectorstores.typesense (conditional)
missing module named typesense - imported by langchain_community.vectorstores.typesense (delayed, conditional, optional)
missing module named timescale_vector - imported by langchain_community.query_constructors.timescalevector (delayed, conditional, optional), langchain_community.vectorstores.timescalevector (delayed, conditional, optional)
missing module named 'tigrisdb.types' - imported by langchain_community.vectorstores.tigris (conditional)
missing module named tigrisdb - imported by langchain_community.vectorstores.tigris (delayed, conditional, optional)
missing module named 'tidb_vector.integrations' - imported by langchain_community.vectorstores.tidb_vector (delayed, optional)
missing module named tidb_vector - imported by langchain_community.vectorstores.tidb_vector (delayed, optional)
missing module named thirdai - imported by langchain_community.retrievers.thirdai_neuraldb (delayed, optional), langchain_community.vectorstores.thirdai_neuraldb (delayed, optional)
missing module named 'zep_python.memory' - imported by langchain_community.retrievers.zep (delayed, conditional)
missing module named nucliadb_protos - imported by langchain_community.tools.nuclia.tool (delayed, optional)
missing module named sklearn - imported by langchain_community.document_transformers.embeddings_redundant_filter (delayed, optional), langchain_community.retrievers.svm (delayed, optional)
missing module named doctran - imported by langchain_community.document_transformers.doctran_text_extract (delayed, optional), langchain_community.document_transformers.doctran_text_qa (delayed, optional), langchain_community.document_transformers.doctran_text_translate (delayed, optional)
missing module named vespa - imported by langchain_community.retrievers.vespa_retriever (delayed, optional)
missing module named joblib - imported by langchain_community.retrievers.tfidf (delayed, optional)
missing module named 'sklearn.metrics' - imported by langchain_community.retrievers.tfidf (delayed)
missing module named 'sklearn.feature_extraction' - imported by langchain_community.retrievers.tfidf (delayed, optional)
missing module named tavily - imported by langchain_community.retrievers.tavily_search_api (delayed, optional)
missing module named 'qdrant_client.http' - imported by langchain_community.chains.pebblo_retrieval.enforcement_filters (delayed, optional), langchain_community.vectorstores.qdrant (delayed, conditional), langchain_community.query_constructors.qdrant (delayed, conditional, optional), langchain_community.retrievers.qdrant_sparse_vector_retriever (delayed, optional)
missing module named qdrant_client - imported by langchain_community.chains.pebblo_retrieval.enforcement_filters (delayed, optional), langchain_community.vectorstores.qdrant (delayed, conditional, optional), langchain_community.retrievers.qdrant_sparse_vector_retriever (delayed, optional)
missing module named 'pinecone_text.hybrid' - imported by langchain_community.retrievers.pinecone_hybrid_search (delayed)
missing module named 'pinecone_text.sparse' - imported by langchain_community.retrievers.pinecone_hybrid_search (delayed, optional)
missing module named pinecone_text - imported by langchain_community.retrievers.pinecone_hybrid_search (delayed, optional)
missing module named nanopq - imported by langchain_community.retrievers.nanopq (delayed, optional)
missing module named metal_sdk - imported by langchain_community.retrievers.metal (delayed)
missing module named 'llama_index.core' - imported by langchain_community.retrievers.llama_index (delayed, optional)
missing module named llama_index - imported by langchain_community.retrievers.llama_index (delayed, optional)
missing module named kay - imported by langchain_community.retrievers.kay (delayed, optional)
missing module named embedchain - imported by langchain_community.retrievers.embedchain (delayed)
missing module named 'elasticsearch.helpers' - imported by langchain_community.vectorstores.baiducloud_vector_search (delayed, optional), langchain_community.vectorstores.ecloud_vector_search (delayed, optional), langchain_community.vectorstores.elastic_vector_search (delayed, optional), langchain_community.vectorstores.elasticsearch (delayed, optional), langchain_community.retrievers.elastic_search_bm25 (delayed, optional)
missing module named 'docarray.index' - imported by langchain_community.vectorstores.docarray.base (conditional), langchain_community.vectorstores.docarray.hnsw (delayed), langchain_community.vectorstores.docarray.in_memory (delayed), langchain_community.retrievers.docarray (delayed)
missing module named rank_bm25 - imported by langchain_community.retrievers.bm25 (delayed, optional)
missing module named 'botocore.exceptions' - imported by langchain_community.retrievers.bedrock (delayed, optional)
missing module named 'botocore.client' - imported by langchain_community.retrievers.bedrock (delayed, optional)
missing module named 'asknews_sdk.dto' - imported by langchain_community.retrievers.asknews (delayed)
missing module named langchain_weaviate - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named langchain_qdrant - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named langchain_postgres - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named langchain_chroma - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named langchain_neo4j - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named langchain_mongodb - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named langchain_pinecone - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named langchain_elasticsearch - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named langchain_astradb - imported by langchain.retrievers.self_query.base (delayed, conditional, optional)
missing module named 'redis.commands' - imported by langchain_community.graphs.falkordb_graph (delayed), langchain_community.vectorstores.redis.base (delayed, conditional, optional), langchain_community.vectorstores.redis.schema (delayed, conditional)
missing module named 'redis.client' - imported by langchain_community.utilities.redis (conditional), langchain_community.vectorstores.redis.base (conditional)
missing module named upstash_redis - imported by langchain.memory.entity (delayed, optional), langchain_community.chat_message_histories.upstash_redis (delayed, optional), langchain_community.storage.upstash_redis (delayed, optional)
missing module named pymongo - imported by langchain_community.chat_message_histories.mongodb (delayed), langchain_community.vectorstores.azure_cosmos_db (delayed, optional), langchain_community.vectorstores.documentdb (delayed, optional), langchain_community.vectorstores.mongodb_atlas (delayed, optional), langchain_community.storage.mongodb (delayed, optional)
missing module named lark - imported by langchain.chains.query_constructor.parser (optional)
missing module named tair - imported by langchain_community.vectorstores.tair (delayed, optional)
missing module named tablestore - imported by langchain_community.vectorstores.tablestore (delayed, optional)
missing module named 'surrealdb.ws' - imported by langchain_community.vectorstores.surrealdb (delayed, conditional)
missing module named pymysql - imported by langchain_community.vectorstores.apache_doris (delayed, optional), langchain_community.vectorstores.starrocks (delayed, optional)
missing module named sqlite_vss - imported by langchain_community.vectorstores.sqlitevss (delayed, optional)
missing module named sqlite_vec - imported by langchain_community.vectorstores.sqlitevec (delayed, optional)
missing module named singlestoredb - imported by langchain_community.chat_message_histories.singlestoredb (delayed, optional), langchain_community.vectorstores.singlestoredb (delayed, optional)
missing module named 'pgvecto_rs.sqlalchemy' - imported by langchain_community.vectorstores.relyt (delayed)
missing module named pgvecto_rs - imported by langchain_community.vectorstores.relyt (delayed, optional)
missing module named 'qdrant_client.local' - imported by langchain_community.vectorstores.qdrant (delayed)
missing module named 'qdrant_client.conversions' - imported by langchain_community.vectorstores.qdrant (conditional)
missing module named pinecone - imported by langchain_community.vectorstores.pinecone (delayed, conditional, optional)
missing module named pgvector - imported by langchain_community.vectorstores.pgvector (delayed)
missing module named 'opensearchpy.helpers' - imported by langchain_community.vectorstores.opensearch_vector_search (delayed, optional)
missing module named 'opensearchpy.exceptions' - imported by langchain_community.vectorstores.opensearch_vector_search (delayed, optional)
missing module named opensearchpy - imported by langchain_community.vectorstores.opensearch_vector_search (delayed, conditional, optional)
missing module named 'neo4j.exceptions' - imported by langchain_community.graphs.memgraph_graph (delayed), langchain_community.graphs.neo4j_graph (delayed), langchain_community.vectorstores.neo4j_vector (delayed, conditional)
missing module named neo4j - imported by langchain_community.graphs.memgraph_graph (delayed, optional), langchain_community.graphs.neo4j_graph (delayed, optional), langchain_community.chat_message_histories.neo4j (delayed, optional), langchain_community.vectorstores.neo4j_vector (delayed, optional)
missing module named pyTigerGraph - imported by langchain_community.graphs.tigergraph_graph (delayed, optional)
missing module named 'rdflib.query' - imported by langchain_community.graphs.ontotext_graphdb_graph (delayed), langchain_community.graphs.rdf_graph (delayed)
missing module named 'rdflib.exceptions' - imported by langchain_community.graphs.ontotext_graphdb_graph (delayed), langchain_community.graphs.rdf_graph (delayed)
missing module named 'rdflib.plugins' - imported by langchain_community.graphs.ontotext_graphdb_graph (delayed, optional), langchain_community.graphs.rdf_graph (delayed, optional)
missing module named rdflib - imported by langchain_community.graphs.ontotext_graphdb_graph (delayed, conditional, optional), langchain_community.graphs.rdf_graph (delayed, conditional, optional)
missing module named pyparsing - imported by langchain_community.graphs.ontotext_graphdb_graph (delayed)
missing module named 'networkx.drawing' - imported by langchain_community.graphs.networkx_graph (delayed)
missing module named networkx - imported by langchain_community.graphs.networkx_graph (delayed, optional)
missing module named 'botocore.config' - imported by langchain_community.graphs.neptune_graph (delayed, conditional, optional), langchain_community.graphs.neptune_rdf_graph (delayed, conditional, optional)
missing module named 'nebula3.fbthrift' - imported by langchain_community.graphs.nebula_graph (delayed)
missing module named 'nebula3.gclient' - imported by langchain_community.graphs.nebula_graph (delayed)
missing module named 'nebula3.Exception' - imported by langchain_community.graphs.nebula_graph (delayed)
missing module named 'nebula3.Config' - imported by langchain_community.graphs.nebula_graph (delayed)
missing module named nebula3 - imported by langchain_community.graphs.nebula_graph (delayed, optional)
missing module named kuzu - imported by langchain_community.graphs.kuzu_graph (delayed, optional)
missing module named hugegraph - imported by langchain_community.graphs.hugegraph (delayed, optional)
missing module named gremlin_python - imported by langchain_community.graphs.gremlin_graph (delayed, optional)
missing module named falkordb - imported by langchain_community.graphs.falkordb_graph (delayed)
missing module named arango - imported by langchain_community.graphs.arangodb_graph (delayed, optional)
missing module named clickhouse_connect - imported by langchain_community.vectorstores.clickhouse (delayed, optional), langchain_community.vectorstores.myscale (delayed, optional)
missing module named 'pymongo.driver_info' - imported by langchain_community.vectorstores.mongodb_atlas (delayed, optional)
missing module named 'pymongo.collection' - imported by langchain_community.vectorstores.azure_cosmos_db (conditional), langchain_community.vectorstores.documentdb (conditional), langchain_community.vectorstores.mongodb_atlas (conditional)
missing module named 'momento.responses' - imported by langchain_community.chat_message_histories.momento (delayed), langchain_community.vectorstores.momento_vector_index (delayed)
missing module named 'momento.requests' - imported by langchain_community.chat_message_histories.momento (delayed, optional), langchain_community.vectorstores.momento_vector_index (delayed)
missing module named momento - imported by langchain_community.chat_message_histories.momento (delayed, conditional, optional), langchain_community.vectorstores.momento_vector_index (delayed, conditional, optional)
missing module named 'pymilvus.client' - imported by langchain_community.vectorstores.milvus (delayed)
missing module named 'pymilvus.orm' - imported by langchain_community.vectorstores.milvus (delayed)
missing module named meilisearch - imported by langchain_community.vectorstores.meilisearch (delayed, conditional, optional)
missing module named marqo - imported by langchain_community.vectorstores.marqo (delayed, conditional, optional)
missing module named 'manticoresearch.api_client' - imported by langchain_community.vectorstores.manticore_search (delayed, optional)
missing module named manticoresearch - imported by langchain_community.vectorstores.manticore_search (delayed, optional)
missing module named kdbai_client - imported by langchain_community.vectorstores.kdbai (delayed, optional)
missing module named hologres_vector - imported by langchain_community.vectorstores.hologres (delayed)
missing module named hdbcli - imported by langchain_community.vectorstores.hanavector (conditional)
missing module named __builtin__ - imported by faiss.swigfaiss_avx2 (optional), faiss.swigfaiss (optional)
missing module named pyepsilla - imported by langchain_community.vectorstores.epsilla (delayed, conditional, optional)
missing module named 'elasticsearch.exceptions' - imported by langchain_community.vectorstores.elastic_vector_search (delayed, optional)
missing module named 'bson.objectid' - imported by langchain_community.vectorstores.documentdb (delayed, optional)
missing module named 'docarray.typing' - imported by langchain_community.vectorstores.docarray.base (delayed)
missing module named docarray - imported by langchain_community.vectorstores.docarray.base (delayed, conditional, optional)
missing module named dingodb - imported by langchain_community.vectorstores.dingo (delayed, optional)
missing module named 'deeplake.util' - imported by langchain_community.vectorstores.deeplake (optional)
missing module named 'deeplake.core' - imported by langchain_community.vectorstores.deeplake (optional)
missing module named deeplake - imported by langchain_community.vectorstores.deeplake (delayed, optional)
missing module named 'databricks.vector_search' - imported by langchain_community.vectorstores.databricks_vector_search (delayed, conditional, optional)
missing module named dashvector - imported by langchain_community.vectorstores.dashvector (delayed, optional)
missing module named 'couchbase.vector_search' - imported by langchain_community.vectorstores.couchbase (delayed)
missing module named 'couchbase.search' - imported by langchain_community.vectorstores.couchbase (delayed)
missing module named 'couchbase.exceptions' - imported by langchain_community.vectorstores.couchbase (delayed)
missing module named clarifai_grpc - imported by langchain_community.vectorstores.clarifai (delayed, optional)
missing module named 'chromadb.utils' - imported by langchain_community.vectorstores.chroma (delayed, conditional)
missing module named 'chromadb.api' - imported by langchain_community.vectorstores.chroma (conditional)
missing module named 'chromadb.config' - imported by langchain_community.vectorstores.chroma (delayed, conditional, optional)
missing module named chromadb - imported by langchain_community.vectorstores.chroma (delayed, conditional, optional)
missing module named 'cassio.table' - imported by langchain_community.chat_message_histories.cassandra (delayed, conditional, optional), langchain_community.vectorstores.cassandra (delayed, optional)
missing module named 'bagel.api' - imported by langchain_community.vectorstores.bagel (conditional)
missing module named 'bagel.config' - imported by langchain_community.vectorstores.bagel (delayed, conditional, optional)
missing module named bagel - imported by langchain_community.vectorstores.bagel (delayed, conditional, optional)
missing module named 'azure.search' - imported by langchain_community.vectorstores.azuresearch (delayed, conditional, optional)
missing module named 'azure.cosmos' - imported by langchain_community.chat_message_histories.cosmos_db (delayed, conditional, optional), langchain_community.vectorstores.azure_cosmos_db_no_sql (conditional)
missing module named bson - imported by langchain_community.vectorstores.azure_cosmos_db (delayed, optional)
missing module named nomic - imported by langchain_community.vectorstores.atlas (delayed, optional)
missing module named 'aperturedb.ParallelLoader' - imported by langchain_community.vectorstores.aperturedb (delayed)
missing module named 'aperturedb.Descriptors' - imported by langchain_community.vectorstores.aperturedb (delayed)
missing module named 'aperturedb.Utils' - imported by langchain_community.vectorstores.aperturedb (delayed, optional)
missing module named aperturedb - imported by langchain_community.vectorstores.aperturedb (delayed, optional)
missing module named 'alibabacloud_ha3engine_vector.models' - imported by langchain_community.vectorstores.alibabacloud_opensearch (delayed)
missing module named alibabacloud_tea_util - imported by langchain_community.vectorstores.alibabacloud_opensearch (delayed, optional)
missing module named alibabacloud_ha3engine_vector - imported by langchain_community.vectorstores.alibabacloud_opensearch (delayed, optional)
missing module named 'aerospike_vector_search.types' - imported by langchain_community.vectorstores.aerospike (delayed, conditional)
missing module named aerospike_vector_search - imported by langchain_community.vectorstores.aerospike (delayed, conditional, optional)
missing module named 'nltk.translate' - imported by langchain_community.example_selectors.ngram_overlap (delayed, optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named faiss.swigfaiss_sve - imported by faiss.loader (conditional, optional)
missing module named faiss.swigfaiss_avx512 - imported by faiss.loader (conditional, optional)
missing module named faiss.swigfaiss_avx512_spr - imported by faiss.loader (conditional, optional)
missing module named 'numpy.distutils' - imported by faiss.loader (delayed, conditional)
missing module named multion - imported by langchain_community.tools.multion.close_session (conditional, optional), langchain_community.tools.multion.create_session (conditional, optional), langchain_community.tools.multion.update_session (conditional, optional)
missing module named amadeus - imported by langchain_community.tools.amadeus.utils (delayed, conditional, optional), langchain_community.tools.amadeus.base (conditional), langchain_community.tools.amadeus.flight_search (delayed, optional), langchain_community.agent_toolkits.amadeus.toolkit (conditional)
missing module named xata - imported by langchain_community.chat_message_histories.xata (delayed, optional)
missing module named _watchdog_fsevents - imported by watchdog.observers.fsevents (top-level)
missing module named xarray - imported by streamlit.dataframe_util (delayed, conditional)
missing module named 'authlib.jose' - imported by streamlit.auth_util (delayed, optional)
missing module named 'snowflake.connector' - imported by streamlit.connections.snowflake_connection (delayed, conditional)
missing module named 'pandas.lib' - imported by altair.utils.core (optional)
missing module named altair_saver - imported by altair.utils.mimebundle (delayed, conditional, optional)
missing module named vega_datasets - imported by altair.datasets (delayed)
missing module named altair_viewer - imported by altair.vegalite.v4.api (delayed, optional)
missing module named 'tensorflow.python' - imported by streamlit.elements.write (delayed, conditional)
missing module named 'plotly.tools' - imported by streamlit.elements.plotly_chart (delayed)
missing module named 'plotly.io' - imported by streamlit.elements.lib.streamlit_plotly_theme (delayed), streamlit.elements.plotly_chart (delayed)
missing module named 'plotly.basedatatypes' - imported by streamlit.elements.plotly_chart (conditional)
missing module named 'plotly.graph_objs' - imported by streamlit.elements.plotly_chart (conditional)
missing module named 'plotly.graph_objects' - imported by streamlit.elements.lib.streamlit_plotly_theme (delayed)
missing module named sympy - imported by streamlit.type_util (delayed, conditional, optional), streamlit.elements.markdown (delayed, conditional)
missing module named graphviz - imported by streamlit.type_util (conditional), streamlit.elements.graphviz_chart (conditional)
missing module named 'bokeh.embed' - imported by streamlit.elements.bokeh_chart (delayed)
missing module named bokeh - imported by streamlit.elements.bokeh_chart (delayed, conditional)
missing module named plotly - imported by streamlit.type_util (conditional)
missing module named 'psycopg.rows' - imported by langchain_community.chat_message_histories.postgres (delayed)
missing module named 'confluent_kafka.admin' - imported by langchain_community.chat_message_histories.kafka (delayed, conditional, optional)
missing module named confluent_kafka - imported by langchain_community.chat_message_histories.kafka (delayed, conditional, optional)
missing module named firebase_admin - imported by langchain_community.chat_message_histories.firestore (delayed, optional)
missing module named 'dynamodb_encryption_sdk.structures' - imported by langchain_community.chat_message_histories.dynamodb (delayed, conditional, optional)
missing module named 'dynamodb_encryption_sdk.material_providers' - imported by langchain_community.chat_message_histories.dynamodb (delayed, conditional, optional)
missing module named 'dynamodb_encryption_sdk.identifiers' - imported by langchain_community.chat_message_histories.dynamodb (delayed, conditional, optional)
missing module named dynamodb_encryption_sdk - imported by langchain_community.chat_message_histories.dynamodb (delayed, conditional, optional)
missing module named 'langchain_experimental.sql' - imported by langchain.chains.loading (delayed)
missing module named 'langchain_experimental.pal_chain' - imported by langchain.chains.loading (delayed)
missing module named 'langchain_experimental.llm_bash' - imported by langchain.chains.loading (delayed)
missing module named _suggestions - imported by traceback (delayed, optional)
