#!/usr/bin/env python3
"""
Widget Configuration Manager

Handles configuration for the standalone widget version, including
bundled resources and default settings that work without external files.
"""

import os
import sys
import tempfile
import configparser
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class WidgetConfig:
    """Configuration manager for the widget version"""
    
    def __init__(self):
        self.config = configparser.ConfigParser()
        self.temp_dir = None
        self._load_config()
        
    def _get_resource_path(self, relative_path):
        """Get absolute path to resource, works for dev and for PyInstaller"""
        try:
            # PyInstaller creates a temp folder and stores path in _MEIPASS
            base_path = sys._MEIPASS
        except Exception:
            base_path = os.path.abspath(".")
        
        return os.path.join(base_path, relative_path)
    
    def _load_config(self):
        """Load configuration from files or use defaults"""
        # Default configuration
        self._set_defaults()
        
        # Try to load from config files
        config_dir = Path(self._get_resource_path("config"))
        
        # Load default config if available
        default_config = config_dir / "default.ini"
        if default_config.exists():
            try:
                self.config.read(str(default_config))
                logger.info(f"Loaded default config from {default_config}")
            except Exception as e:
                logger.warning(f"Could not load default config: {e}")
        
        # Load local config if available
        local_config = config_dir / "local.ini"
        if local_config.exists():
            try:
                self.config.read(str(local_config))
                logger.info(f"Loaded local config from {local_config}")
            except Exception as e:
                logger.warning(f"Could not load local config: {e}")
        
        # Load environment variables
        self._load_from_env()
    
    def _set_defaults(self):
        """Set default configuration values"""
        # RAG Configuration
        self.config.add_section('rag')
        self.config.set('rag', 'prefix', 'Which application should be used for the following inquiry:\n')
        self.config.set('rag', 'forms_path', 'forms.json')
        self.config.set('rag', 'file_path', 'ZI102.pdf')
        self.config.set('rag', 'index_path', 'rag_index')
        self.config.set('rag', 'model_name', 'gpt-4o')
        
        # Audio Configuration
        self.config.add_section('audio')
        self.config.set('audio', 'rate', '16000')
        self.config.set('audio', 'pause_threshold', '1.0')
        
        # Transcription Configuration
        self.config.add_section('transcription')
        self.config.set('transcription', 'model', 'gpt-4o-transcribe')
        self.config.set('transcription', 'language', 'en')
        self.config.set('transcription', 'prompt', 'This is a conversation about accounting and cash receipts. Please transcribe accurately.')
        self.config.set('transcription', 'temperature', '0.0')
        
        # UI Configuration
        self.config.add_section('ui')
        self.config.set('ui', 'page_title', 'ERA-IGNITE Voice Assistant')
        self.config.set('ui', 'layout', 'wide')
        
        # Application Configuration
        self.config.add_section('application')
        self.config.set('application', 'default_application', 'Accounting')
        self.config.set('application', 'default_form', 'PRINT_CASH_RECEIPTS')
        
        # Agent Configuration
        self.config.add_section('agent')
        self.config.set('agent', 'verbose', 'true')
        
        # TTS Configuration
        self.config.add_section('tts')
        self.config.set('tts', 'enable_tts', 'true')
        self.config.set('tts', 'autoplay_audio', 'true')
        
        # Widget-specific Configuration
        self.config.add_section('widget')
        self.config.set('widget', 'window_width', '1000')
        self.config.set('widget', 'window_height', '700')
        self.config.set('widget', 'websocket_host', 'localhost')
        self.config.set('widget', 'websocket_port', '8765')
        self.config.set('widget', 'enable_system_tray', 'true')
        self.config.set('widget', 'auto_start_server', 'true')
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        env_mappings = {
            'OPENAI_API_KEY': ('api', 'openai_api_key'),
            'RAG_MODEL_NAME': ('rag', 'model_name'),
            'TRANSCRIPTION_MODEL': ('transcription', 'model'),
            'AUDIO_RATE': ('audio', 'rate'),
            'ENABLE_TTS': ('tts', 'enable_tts'),
            'PAGE_TITLE': ('ui', 'page_title'),
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                if not self.config.has_section(section):
                    self.config.add_section(section)
                self.config.set(section, key, value)
    
    def get_temp_dir(self):
        """Get or create temporary directory for widget files"""
        if self.temp_dir is None:
            self.temp_dir = tempfile.mkdtemp(prefix='voice_widget_')
        return self.temp_dir
    
    def cleanup_temp_dir(self):
        """Clean up temporary directory"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            self.temp_dir = None
    
    # Property accessors for easy access to configuration values
    @property
    def model_name(self):
        return self.config.get('rag', 'model_name', fallback='gpt-4o')
    
    @property
    def audio_rate(self):
        return self.config.getint('audio', 'rate', fallback=16000)
    
    @property
    def pause_threshold(self):
        return self.config.getfloat('audio', 'pause_threshold', fallback=1.0)
    
    @property
    def transcription_model(self):
        return self.config.get('transcription', 'model', fallback='gpt-4o-transcribe')
    
    @property
    def transcription_language(self):
        return self.config.get('transcription', 'language', fallback='en')
    
    @property
    def transcription_prompt(self):
        return self.config.get('transcription', 'prompt', 
                             fallback='This is a conversation about accounting and cash receipts. Please transcribe accurately.')
    
    @property
    def transcription_temperature(self):
        return self.config.getfloat('transcription', 'temperature', fallback=0.0)
    
    @property
    def enable_tts(self):
        return self.config.getboolean('tts', 'enable_tts', fallback=True)
    
    @property
    def autoplay_audio(self):
        return self.config.getboolean('tts', 'autoplay_audio', fallback=True)
    
    @property
    def page_title(self):
        return self.config.get('ui', 'page_title', fallback='ERA-IGNITE Voice Assistant')
    
    @property
    def page_icon(self):
        return "🎙️"  # Hardcoded emoji to avoid encoding issues
    
    @property
    def layout(self):
        return self.config.get('ui', 'layout', fallback='wide')
    
    @property
    def default_application(self):
        return self.config.get('application', 'default_application', fallback='Accounting')
    
    @property
    def default_form(self):
        return self.config.get('application', 'default_form', fallback='PRINT_CASH_RECEIPTS')
    
    @property
    def agent_verbose(self):
        return self.config.getboolean('agent', 'verbose', fallback=True)
    
    @property
    def forms_path(self):
        return self._get_resource_path(self.config.get('rag', 'forms_path', fallback='forms.json'))
    
    @property
    def file_path(self):
        return self._get_resource_path(self.config.get('rag', 'file_path', fallback='ZI102.pdf'))
    
    @property
    def index_path(self):
        return self._get_resource_path(self.config.get('rag', 'index_path', fallback='rag_index'))
    
    # Widget-specific properties
    @property
    def window_width(self):
        return self.config.getint('widget', 'window_width', fallback=1000)
    
    @property
    def window_height(self):
        return self.config.getint('widget', 'window_height', fallback=700)
    
    @property
    def websocket_host(self):
        return self.config.get('widget', 'websocket_host', fallback='localhost')
    
    @property
    def websocket_port(self):
        return self.config.getint('widget', 'websocket_port', fallback=8765)
    
    @property
    def enable_system_tray(self):
        return self.config.getboolean('widget', 'enable_system_tray', fallback=True)
    
    @property
    def auto_start_server(self):
        return self.config.getboolean('widget', 'auto_start_server', fallback=True)
    
    def get_config_info(self):
        """Get configuration information for display"""
        config_dir = Path(self._get_resource_path("config"))
        return {
            'config_dir': str(config_dir),
            'default_exists': (config_dir / "default.ini").exists(),
            'local_exists': (config_dir / "local.ini").exists(),
            'resource_path': self._get_resource_path(""),
            'temp_dir': self.get_temp_dir()
        }

# Global widget config instance
_widget_config = None

def get_widget_config():
    """Get the global widget configuration instance"""
    global _widget_config
    if _widget_config is None:
        _widget_config = WidgetConfig()
    return _widget_config

def cleanup_widget_config():
    """Clean up the global widget configuration"""
    global _widget_config
    if _widget_config:
        _widget_config.cleanup_temp_dir()
        _widget_config = None
