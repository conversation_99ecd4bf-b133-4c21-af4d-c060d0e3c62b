# Troubleshooting Port Conflicts

## 🚨 Error: "Only one usage of each socket address is normally permitted"

This error occurs when port 8765 (or another port) is already in use by another process.

## 🔍 Quick Diagnosis

### Check what's using the port:
```bash
# Windows
netstat -ano | findstr :8765

# The output shows P<PERSON> in the last column
# Example: TCP 127.0.0.1:8765 0.0.0.0:0 LISTENING 12345
```

## 🛠️ Solutions (Choose One)

### Solution 1: Kill Existing Processes (Recommended)

#### Option A: Use our automated script
```bash
python kill_voice_servers.py
```

#### Option B: Use Windows batch file
```bash
fix_port_conflict.bat
```

#### Option C: Manual process killing
```bash
# Find the PID from netstat output, then:
taskkill /PID <PID_NUMBER> /F

# Example:
taskkill /PID 12345 /F
```

### Solution 2: Use Different Port

#### Option A: Start server with auto-port selection
```bash
python start_voice_server.py --auto-port
```

#### Option B: Specify a different port
```bash
python start_voice_server.py --port 8766
```

#### Option C: Use widget with different port
Update `config/local.ini`:
```ini
[widget]
websocket_port = 8766
```

### Solution 3: Restart Your Computer
If other solutions don't work, a restart will clear all processes.

## 🔧 Prevention

### 1. Always Close Properly
- Use Ctrl+C to stop servers gracefully
- Close widget applications properly (don't force-quit)
- Use the "Quit" button in the widget

### 2. Check Before Starting
```bash
# Check if port is available before starting
python -c "import socket; s = socket.socket(); s.bind(('localhost', 8765)); print('Port available'); s.close()"
```

### 3. Use Auto-Port Feature
Always start with `--auto-port` flag:
```bash
python start_voice_server.py --auto-port
```

## 🎯 Widget-Specific Solutions

The widget now includes automatic port conflict resolution:

### Updated Widget Features:
- **Auto-port detection** - Finds available ports automatically
- **Graceful fallback** - Tries alternative ports if default is busy
- **Clear error messages** - Shows exactly what went wrong
- **Port status display** - Shows which port is actually being used

### Widget Configuration:
```ini
# config/local.ini
[widget]
websocket_port = 8765  # Default port
auto_start_server = true  # Auto-start with port detection
```

## 📋 Step-by-Step Resolution

### For Widget Users:
1. **Close any running widgets** completely
2. **Run the port conflict resolver:**
   ```bash
   fix_port_conflict.bat
   ```
3. **Start the widget** - it will auto-detect available ports
4. **If still failing**, restart your computer

### For Developers:
1. **Check running processes:**
   ```bash
   python kill_voice_servers.py
   ```
2. **Use auto-port mode:**
   ```bash
   python start_voice_server.py --auto-port
   ```
3. **Test with different port:**
   ```bash
   python start_voice_server.py --port 8766
   ```

## 🔍 Advanced Debugging

### Find all voice-related processes:
```bash
# Windows PowerShell
Get-Process | Where-Object {$_.ProcessName -like "*python*" -or $_.CommandLine -like "*voice*"}

# Command Prompt
tasklist | findstr python
```

### Check all network connections:
```bash
netstat -ano | findstr LISTENING
```

### Kill all Python processes (nuclear option):
```bash
taskkill /IM python.exe /F
```

## ⚠️ Common Causes

1. **Previous server didn't shut down cleanly**
2. **Multiple widget instances running**
3. **Development server still running in background**
4. **Another application using port 8765**
5. **Crashed processes holding the port**

## ✅ Verification

After applying a solution, verify it worked:

```bash
# Test port availability
python -c "import socket; s = socket.socket(); s.bind(('localhost', 8765)); print('✅ Port 8765 is available'); s.close()"

# Start server to confirm
python start_voice_server.py --auto-port
```

## 🆘 If Nothing Works

1. **Restart your computer** - This will clear all processes
2. **Check antivirus software** - May be blocking network access
3. **Try a different port range** - Some ports may be reserved
4. **Run as administrator** - May need elevated privileges
5. **Check Windows Firewall** - May be blocking the application

## 📞 Getting Help

If you're still having issues:

1. **Run the diagnostic script:**
   ```bash
   python kill_voice_servers.py
   ```

2. **Collect information:**
   - Output of `netstat -ano | findstr :8765`
   - Error messages from the widget
   - Your operating system version

3. **Try the native audio version:**
   ```bash
   python widget_launcher_advanced.py
   # Choose option 2 (Native Audio Version)
   ```

The port conflict issue is now **automatically handled** by the updated widget, but these manual solutions are available if needed!
