@echo off
echo ========================================
echo Voice Server Port Conflict Resolver
echo ========================================
echo.

echo Checking for processes using port 8765...
netstat -ano | findstr :8765

if %errorlevel% == 0 (
    echo.
    echo Found processes using port 8765.
    echo.
    echo Options:
    echo 1. Kill processes using port 8765
    echo 2. Use Python script to kill voice servers
    echo 3. Start server with different port
    echo 4. Exit
    echo.
    set /p choice="Enter your choice (1-4): "
    
    if "%choice%"=="1" (
        echo.
        echo Killing processes using port 8765...
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8765') do (
            echo Killing process %%a
            taskkill /PID %%a /F
        )
        echo Done.
    ) else if "%choice%"=="2" (
        echo.
        echo Running Python process killer...
        python kill_voice_servers.py
    ) else if "%choice%"=="3" (
        echo.
        echo Starting server with auto-port selection...
        python start_voice_server.py --auto-port
    ) else if "%choice%"=="4" (
        echo Exiting...
        goto :end
    ) else (
        echo Invalid choice.
    )
) else (
    echo Port 8765 is available.
    echo You can start the voice server normally.
)

:end
echo.
pause
