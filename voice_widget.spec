# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Data files to include
datas = [
    ('config', 'config'),
    ('prompts', 'prompts'),
    ('forms.json', '.'),
    ('questionnaire_config.json', '.'),
    ('voice_streaming_component.html', '.'),
    ('ZI102.pdf', '.'),
]

# Hidden imports that PyInstaller might miss
hiddenimports = [
    'websockets',
    'langchain',
    'langchain_community',
    'langchain_openai',
    'langchain_litellm',
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'certifi',
    'dotenv',
    'tiktoken',
    'faiss',
    'numpy',
    'psutil',
    'multipart',
    'pydantic',
    'pydantic_core',
    'agent.agents',
    'src.config_manager',
    'questionnaire_handler',
    'utils',
]

a = Analysis(
    ['voice_streaming_widget.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VoiceStreamingWidget',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='widget_icon.ico' if os.path.exists('widget_icon.ico') else None,
)
