#!/usr/bin/env python3
"""
Voice Streaming Widget Launcher

Simple launcher script that checks dependencies and starts the widget application.
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'PyQt6',
        'websockets',
        'langchain',
        'certifi',
        'dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'dotenv':
                importlib.import_module('dotenv')
            else:
                importlib.import_module(package.lower())
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(missing_packages):
    """Install missing dependencies"""
    if not missing_packages:
        return True
        
    print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
    
    try:
        # Try to install from widget requirements
        if os.path.exists('requirements-widget.txt'):
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements-widget.txt'])
        else:
            # Install individual packages
            for package in missing_packages:
                if package == 'dotenv':
                    package = 'python-dotenv'
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        
        print("✅ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_files():
    """Check if required files exist"""
    required_files = [
        'voice_streaming_widget.py',
        'websocket_voice_server.py',
        'utils.py',
        'questionnaire_handler.py'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    return missing_files

def setup_environment():
    """Setup environment variables and configuration"""
    # Set SSL certificates
    try:
        import certifi
        cert_path = certifi.where()
        os.environ['SSL_CERT_FILE'] = cert_path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['CURL_CA_BUNDLE'] = cert_path
        print("✅ SSL certificates configured")
    except ImportError:
        print("⚠️  SSL certificates not configured (certifi not available)")
    
    # Load .env file if it exists
    if os.path.exists('.env'):
        try:
            from dotenv import load_dotenv
            load_dotenv()
            print("✅ Environment variables loaded from .env")
        except ImportError:
            print("⚠️  Could not load .env file (python-dotenv not available)")
    
    # Ensure config directory exists
    config_dir = Path("config")
    if not config_dir.exists():
        config_dir.mkdir()
        print("✅ Created config directory")
    
    # Check for configuration files
    default_config = config_dir / "default.ini"
    if not default_config.exists():
        print("⚠️  Default configuration not found, widget will use built-in defaults")
    else:
        print("✅ Configuration files found")

def main():
    """Main launcher function"""
    print("🎙️ Voice Streaming Widget Launcher")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        sys.exit(1)
    
    print("\n📋 Checking dependencies...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        response = input("Would you like to install them automatically? (y/n): ")
        
        if response.lower() in ['y', 'yes']:
            if not install_dependencies(missing_packages):
                input("Press Enter to exit...")
                sys.exit(1)
        else:
            print("❌ Cannot start widget without required dependencies")
            input("Press Enter to exit...")
            sys.exit(1)
    
    print("\n📁 Checking required files...")
    missing_files = check_files()
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        print("Please ensure you have all the necessary files in the current directory.")
        input("Press Enter to exit...")
        sys.exit(1)
    
    print("\n⚙️  Setting up environment...")
    setup_environment()
    
    print("\n🚀 Starting Voice Streaming Widget...")
    print("=" * 50)
    
    try:
        # Import and run the widget
        from voice_streaming_widget import main as widget_main
        widget_main()
        
    except ImportError as e:
        print(f"❌ Failed to import widget: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting widget: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    # Add current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # Import pathlib after adding to path
    from pathlib import Path
    
    main()
