@echo off
echo ========================================
echo Voice Streaming Widget Build Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

echo Python found, starting build process...
echo.

REM Run the build script
python build_widget.py

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Output files:
echo - dist\VoiceStreamingWidget.exe
echo - dist\VoiceStreamingWidget_Portable\
echo.
echo You can now run the executable or distribute the portable package.
echo.
pause
