#!/usr/bin/env python3
"""
Kill Voice Servers Utility

This script helps kill any existing voice server processes that might be
blocking the default port (8765) or other ports.
"""

import os
import sys
import subprocess
import psutil
import signal

def find_processes_using_port(port):
    """Find processes using a specific port"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # Check if process has network connections
            connections = proc.connections()
            for conn in connections:
                if conn.laddr.port == port:
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return processes

def find_voice_server_processes():
    """Find processes that look like voice servers"""
    voice_processes = []
    keywords = ['websocket_voice_server', 'voice_streaming', 'start_voice_server', 'VoiceStreamingWidget']
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            name = proc.info['name']
            
            # Check if any keyword is in the command line or process name
            if any(keyword in cmdline.lower() or keyword.lower() in name.lower() for keyword in keywords):
                voice_processes.append({
                    'pid': proc.info['pid'],
                    'name': name,
                    'cmdline': cmdline
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    return voice_processes

def kill_process(pid, force=False):
    """Kill a process by PID"""
    try:
        proc = psutil.Process(pid)
        if force:
            proc.kill()  # SIGKILL
        else:
            proc.terminate()  # SIGTERM
        return True
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        return False

def main():
    """Main function"""
    print("🔍 Voice Server Process Killer")
    print("=" * 40)
    
    # Check for processes using common voice server ports
    common_ports = [8765, 8766, 8767, 8768]
    port_processes = {}
    
    print("🔍 Checking for processes using voice server ports...")
    for port in common_ports:
        processes = find_processes_using_port(port)
        if processes:
            port_processes[port] = processes
            print(f"📍 Port {port}: {len(processes)} process(es) found")
        else:
            print(f"✅ Port {port}: Available")
    
    # Check for voice server processes by name/command
    print("\n🔍 Checking for voice server processes...")
    voice_processes = find_voice_server_processes()
    
    if voice_processes:
        print(f"📍 Found {len(voice_processes)} voice server process(es):")
        for proc in voice_processes:
            print(f"   PID {proc['pid']}: {proc['name']} - {proc['cmdline'][:80]}...")
    else:
        print("✅ No voice server processes found")
    
    # Combine all processes to kill
    all_processes = {}
    
    # Add port-using processes
    for port, processes in port_processes.items():
        for proc in processes:
            all_processes[proc['pid']] = {**proc, 'reason': f'using port {port}'}
    
    # Add voice server processes
    for proc in voice_processes:
        if proc['pid'] not in all_processes:
            all_processes[proc['pid']] = {**proc, 'reason': 'voice server process'}
    
    if not all_processes:
        print("\n🎉 No processes need to be killed. Ports should be available.")
        return
    
    print(f"\n⚠️  Found {len(all_processes)} process(es) to kill:")
    for pid, proc in all_processes.items():
        print(f"   PID {pid}: {proc['name']} ({proc['reason']})")
    
    print("\nOptions:")
    print("1. Kill all found processes (graceful)")
    print("2. Force kill all found processes")
    print("3. Kill specific process by PID")
    print("4. Exit without killing")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == '1':
        print("\n🔄 Killing processes gracefully...")
        killed = 0
        for pid in all_processes.keys():
            if kill_process(pid, force=False):
                print(f"✅ Killed process {pid}")
                killed += 1
            else:
                print(f"❌ Failed to kill process {pid}")
        print(f"\n🎉 Killed {killed}/{len(all_processes)} processes")
        
    elif choice == '2':
        print("\n💥 Force killing processes...")
        killed = 0
        for pid in all_processes.keys():
            if kill_process(pid, force=True):
                print(f"✅ Force killed process {pid}")
                killed += 1
            else:
                print(f"❌ Failed to force kill process {pid}")
        print(f"\n🎉 Force killed {killed}/{len(all_processes)} processes")
        
    elif choice == '3':
        try:
            pid = int(input("Enter PID to kill: "))
            force = input("Force kill? (y/n): ").lower().startswith('y')
            
            if kill_process(pid, force=force):
                print(f"✅ {'Force ' if force else ''}Killed process {pid}")
            else:
                print(f"❌ Failed to kill process {pid}")
        except ValueError:
            print("❌ Invalid PID")
            
    elif choice == '4':
        print("👋 Exiting without killing processes")
        
    else:
        print("❌ Invalid choice")
    
    # Check ports again
    print("\n🔍 Checking ports after cleanup...")
    for port in common_ports:
        processes = find_processes_using_port(port)
        if processes:
            print(f"⚠️  Port {port}: Still {len(processes)} process(es)")
        else:
            print(f"✅ Port {port}: Now available")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    
    input("\nPress Enter to exit...")
