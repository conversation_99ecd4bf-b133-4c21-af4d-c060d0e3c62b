#!/usr/bin/env python3
"""
Build script for creating the Voice Streaming Widget executable

This script uses PyInstaller to create a standalone executable that includes
all dependencies and can run on any Windows machine without Python installed.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """Check if PyInstaller is available"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller version: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller not found")
        return False

def install_pyinstaller():
    """Install PyInstaller"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'PyInstaller'])
        print("✅ PyInstaller installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install PyInstaller")
        return False

def create_spec_file():
    """Create PyInstaller spec file for the widget"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Data files to include
datas = [
    ('config', 'config'),
    ('prompts', 'prompts'),
    ('forms.json', '.'),
    ('questionnaire_config.json', '.'),
    ('voice_streaming_component.html', '.'),
    ('ZI102.pdf', '.'),
]

# Hidden imports that PyInstaller might miss
hiddenimports = [
    'websockets',
    'langchain',
    'langchain_community',
    'langchain_openai',
    'langchain_litellm',
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'certifi',
    'dotenv',
    'tiktoken',
    'faiss',
    'numpy',
    'psutil',
    'multipart',
    'pydantic',
    'pydantic_core',
    'agent.agents',
    'src.config_manager',
    'questionnaire_handler',
    'utils',
]

a = Analysis(
    ['voice_streaming_widget.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VoiceStreamingWidget',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='widget_icon.ico' if os.path.exists('widget_icon.ico') else None,
)
'''
    
    with open('voice_widget.spec', 'w') as f:
        f.write(spec_content)
    
    print("✅ Created PyInstaller spec file: voice_widget.spec")

def build_executable():
    """Build the executable using PyInstaller"""
    try:
        # Clean previous builds
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        print("🧹 Cleaned previous build directories")
        
        # Run PyInstaller
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'voice_widget.spec']
        
        print("🔨 Building executable...")
        print(f"Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build completed successfully!")
            
            # Check if executable was created
            exe_path = Path('dist/VoiceStreamingWidget.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📦 Executable created: {exe_path}")
                print(f"📏 Size: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Executable not found in dist directory")
                return False
        else:
            print("❌ Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_portable_package():
    """Create a portable package with the executable and required files"""
    try:
        # Create portable directory
        portable_dir = Path('dist/VoiceStreamingWidget_Portable')
        portable_dir.mkdir(exist_ok=True)
        
        # Copy executable
        exe_source = Path('dist/VoiceStreamingWidget.exe')
        if exe_source.exists():
            shutil.copy2(exe_source, portable_dir / 'VoiceStreamingWidget.exe')
            print("✅ Copied executable to portable package")
        
        # Create README for portable package
        readme_content = """# Voice Streaming Widget - Portable Version

## Quick Start

1. Double-click `VoiceStreamingWidget.exe` to start the application
2. Allow microphone access when prompted
3. Click "Start Streaming" and begin speaking
4. The AI will respond automatically

## Features

- 🎙️ Continuous voice streaming (no button presses needed)
- 🤖 AI-powered responses
- 💬 Real-time chat interface
- 🔊 Text-to-speech audio responses
- 🖥️ System tray integration

## System Requirements

- Windows 10 or later
- Microphone access
- Internet connection (for AI processing)

## Troubleshooting

### If the application won't start:
- Right-click and "Run as administrator"
- Check Windows Defender/antivirus settings
- Ensure microphone permissions are enabled

### If voice streaming doesn't work:
- Check microphone permissions in Windows Settings
- Try a different microphone or headset
- Restart the application

### If AI responses are slow:
- Check your internet connection
- Verify API keys are configured (if using custom models)

## Configuration

The application uses default settings that work out of the box. For advanced
configuration, you can create a `config` folder with custom settings.

## Support

For issues or questions, please refer to the project documentation.
"""
        
        with open(portable_dir / 'README.txt', 'w') as f:
            f.write(readme_content)
        
        print("✅ Created portable package with README")
        print(f"📁 Portable package location: {portable_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating portable package: {e}")
        return False

def main():
    """Main build function"""
    print("🔨 Voice Streaming Widget Build Script")
    print("=" * 50)
    
    # Check PyInstaller
    if not check_pyinstaller():
        print("📦 Installing PyInstaller...")
        if not install_pyinstaller():
            print("❌ Cannot proceed without PyInstaller")
            return False
    
    # Check required files
    required_files = [
        'voice_streaming_widget.py',
        'websocket_voice_server.py',
        'utils.py',
        'questionnaire_handler.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files found")
    
    # Create spec file
    print("\n📝 Creating PyInstaller specification...")
    create_spec_file()
    
    # Build executable
    print("\n🔨 Building executable...")
    if not build_executable():
        return False
    
    # Create portable package
    print("\n📦 Creating portable package...")
    if not create_portable_package():
        return False
    
    print("\n🎉 Build completed successfully!")
    print("=" * 50)
    print("📁 Output files:")
    print("   - dist/VoiceStreamingWidget.exe (standalone executable)")
    print("   - dist/VoiceStreamingWidget_Portable/ (portable package)")
    print("\n🚀 You can now distribute the executable or portable package!")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
        sys.exit(1)
