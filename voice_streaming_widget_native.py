#!/usr/bin/env python3
"""
Voice Streaming Widget - Native Audio Version

A standalone desktop widget that uses PyQt6's native audio capabilities
instead of WebEngine for microphone access, avoiding browser permission issues.
"""

import sys
import os
import threading
import asyncio
import logging
import json
import base64
import tempfile
import wave
import numpy as np
from pathlib import Path

# Fix SSL certificates BEFORE any other imports
try:
    import certifi
    cert_path = certifi.where()
    os.environ['SSL_CERT_FILE'] = cert_path
    os.environ['REQUESTS_CA_BUNDLE'] = cert_path
    os.environ['CURL_CA_BUNDLE'] = cert_path
except ImportError:
    pass

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# PyQt6 imports
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, Q<PERSON>ush<PERSON>utton, QLabel, QTextEdit, QProgressBar,
                            QSystemTrayIcon, QMenu, QMessageBox, QSplitter, QScrollArea)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt, QIODevice
from PyQt6.QtGui import QIcon, QFont, QPixmap, QAction
from PyQt6.QtMultimedia import QAudioInput, QAudioFormat, QMediaDevices

# Widget configuration
from widget_config import get_widget_config, cleanup_widget_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioCaptureThread(QThread):
    """Thread for capturing audio using PyQt6's native audio capabilities"""
    
    audio_data_ready = pyqtSignal(bytes)
    audio_level_changed = pyqtSignal(float)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.audio_input = None
        self.audio_device = None
        self.is_recording = False
        self.audio_buffer = b''
        
    def setup_audio(self):
        """Setup audio input device and format"""
        try:
            # Get default audio input device
            devices = QMediaDevices.audioInputs()
            if not devices:
                self.error_occurred.emit("No audio input devices found")
                return False
            
            self.audio_device = QMediaDevices.defaultAudioInput()
            logger.info(f"Using audio device: {self.audio_device.description()}")
            
            # Setup audio format
            format = QAudioFormat()
            format.setSampleRate(16000)
            format.setChannelCount(1)
            format.setSampleFormat(QAudioFormat.SampleFormat.Int16)
            
            # Create audio input
            self.audio_input = QAudioInput(self.audio_device, format)
            
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"Audio setup error: {e}")
            return False
    
    def start_recording(self):
        """Start audio recording"""
        if not self.setup_audio():
            return False
            
        try:
            self.is_recording = True
            self.audio_input.start()
            logger.info("Audio recording started")
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"Recording start error: {e}")
            return False
    
    def stop_recording(self):
        """Stop audio recording"""
        try:
            self.is_recording = False
            if self.audio_input:
                self.audio_input.stop()
            logger.info("Audio recording stopped")
            
        except Exception as e:
            logger.error(f"Recording stop error: {e}")
    
    def run(self):
        """Main thread loop for audio processing"""
        while self.is_recording:
            try:
                # This is a simplified version - in practice, you'd need to
                # implement proper audio data reading from QAudioInput
                self.msleep(100)  # Sleep for 100ms
                
            except Exception as e:
                self.error_occurred.emit(f"Audio processing error: {e}")
                break

class WebSocketServerThread(QThread):
    """Thread to run the WebSocket server"""
    
    server_started = pyqtSignal(str, int)  # host, port
    server_error = pyqtSignal(str)
    
    def __init__(self, host="localhost", port=8765):
        super().__init__()
        self.host = host
        self.port = port
        self.server = None
        
    def run(self):
        """Run the WebSocket server in this thread"""
        try:
            # Import here to avoid issues with event loop
            from websocket_voice_server import start_voice_server
            
            logger.info(f"Starting WebSocket server on {self.host}:{self.port}")
            self.server_started.emit(self.host, self.port)
            
            # Start the server (this will block)
            start_voice_server(host=self.host, port=self.port)
            
        except Exception as e:
            logger.error(f"WebSocket server error: {e}")
            self.server_error.emit(str(e))

class VoiceStreamingWidgetNative(QMainWindow):
    """Main widget application window with native audio"""
    
    def __init__(self):
        super().__init__()
        self.websocket_thread = None
        self.audio_thread = None
        self.server_running = False
        self.is_streaming = False
        
        # Load widget configuration
        self.config = get_widget_config()
        
        # Initialize UI
        self.init_ui()
        
        # Setup system tray
        if self.config.enable_system_tray:
            self.setup_system_tray()
        
        # Start WebSocket server if auto-start is enabled
        if self.config.auto_start_server:
            self.start_websocket_server()
            
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle(f"{self.config.page_icon} {self.config.page_title} - Native Audio")
        self.setGeometry(100, 100, self.config.window_width, self.config.window_height)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel(f"{self.config.page_icon} {self.config.page_title} - Native Audio")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Server status
        self.status_label = QLabel("🔴 Server Starting...")
        header_layout.addWidget(self.status_label)
        
        layout.addLayout(header_layout)
        
        # Voice controls
        controls_widget = QWidget()
        controls_layout = QVBoxLayout(controls_widget)
        
        # Status and buttons
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🎙️ Start Streaming")
        self.start_btn.clicked.connect(self.start_streaming)
        self.start_btn.setEnabled(False)
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ Stop Streaming")
        self.stop_btn.clicked.connect(self.stop_streaming)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        controls_layout.addLayout(button_layout)
        
        # Audio level indicator
        self.audio_level_label = QLabel("Audio Level:")
        controls_layout.addWidget(self.audio_level_label)
        
        self.audio_level_bar = QProgressBar()
        self.audio_level_bar.setRange(0, 100)
        self.audio_level_bar.setValue(0)
        controls_layout.addWidget(self.audio_level_bar)
        
        # Status text
        self.streaming_status = QLabel("Ready to start streaming")
        controls_layout.addWidget(self.streaming_status)
        
        layout.addWidget(controls_widget)
        
        # Chat area
        self.chat_area = QTextEdit()
        self.chat_area.setReadOnly(True)
        self.chat_area.setPlaceholderText("Voice conversation will appear here...")
        layout.addWidget(self.chat_area)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.minimize_btn = QPushButton("Minimize to Tray")
        self.minimize_btn.clicked.connect(self.hide_to_tray)
        button_layout.addWidget(self.minimize_btn)
        
        button_layout.addStretch()
        
        self.restart_btn = QPushButton("Restart Server")
        self.restart_btn.clicked.connect(self.restart_server)
        self.restart_btn.setEnabled(False)
        button_layout.addWidget(self.restart_btn)
        
        self.quit_btn = QPushButton("Quit")
        self.quit_btn.clicked.connect(self.quit_application)
        button_layout.addWidget(self.quit_btn)
        
        layout.addLayout(button_layout)
        
    def setup_system_tray(self):
        """Setup system tray icon and menu"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return
            
        # Create tray icon
        self.tray_icon = QSystemTrayIcon(self)
        
        # Set icon (you can replace this with a custom icon file)
        self.tray_icon.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_ComputerIcon))
        
        # Create tray menu
        tray_menu = QMenu()
        
        show_action = QAction("Show", self)
        show_action.triggered.connect(self.show_from_tray)
        tray_menu.addAction(show_action)
        
        quit_action = QAction("Quit", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        
        # Show tray icon
        self.tray_icon.show()
        
    def start_websocket_server(self):
        """Start the WebSocket server in a separate thread"""
        if self.websocket_thread and self.websocket_thread.isRunning():
            return
            
        self.websocket_thread = WebSocketServerThread(
            host=self.config.websocket_host,
            port=self.config.websocket_port
        )
        self.websocket_thread.server_started.connect(self.on_server_started)
        self.websocket_thread.server_error.connect(self.on_server_error)
        self.websocket_thread.start()
        
    def on_server_started(self, host, port):
        """Handle server started signal"""
        self.server_running = True
        self.status_label.setText(f"🟢 Server Running: {host}:{port}")
        self.restart_btn.setEnabled(True)
        self.start_btn.setEnabled(True)
        self.streaming_status.setText("Server ready - Click 'Start Streaming' to begin")
        
    def on_server_error(self, error_msg):
        """Handle server error signal"""
        self.server_running = False
        self.status_label.setText(f"🔴 Server Error: {error_msg}")
        self.restart_btn.setEnabled(True)
        self.streaming_status.setText(f"Server error: {error_msg}")
        
        # Show error message
        QMessageBox.critical(self, "Server Error", f"WebSocket server failed to start:\n{error_msg}")
        
    def start_streaming(self):
        """Start voice streaming using native audio"""
        if not self.server_running:
            QMessageBox.warning(self, "Server Not Ready", "Please wait for the server to start before streaming.")
            return
            
        try:
            # Setup audio capture
            self.audio_thread = AudioCaptureThread()
            self.audio_thread.audio_data_ready.connect(self.handle_audio_data)
            self.audio_thread.audio_level_changed.connect(self.update_audio_level)
            self.audio_thread.error_occurred.connect(self.handle_audio_error)
            
            if self.audio_thread.start_recording():
                self.audio_thread.start()
                
                self.is_streaming = True
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
                self.streaming_status.setText("🎙️ Streaming active - Speak naturally")
                
                self.add_chat_message("System: Voice streaming started", "system")
            else:
                QMessageBox.critical(self, "Audio Error", "Failed to start audio recording. Please check your microphone.")
                
        except Exception as e:
            QMessageBox.critical(self, "Streaming Error", f"Failed to start streaming: {e}")
            
    def stop_streaming(self):
        """Stop voice streaming"""
        try:
            self.is_streaming = False
            
            if self.audio_thread:
                self.audio_thread.stop_recording()
                self.audio_thread.quit()
                self.audio_thread.wait()
                self.audio_thread = None
            
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.streaming_status.setText("Streaming stopped - Ready to start again")
            self.audio_level_bar.setValue(0)
            
            self.add_chat_message("System: Voice streaming stopped", "system")
            
        except Exception as e:
            logger.error(f"Error stopping streaming: {e}")
            
    def handle_audio_data(self, audio_data):
        """Handle audio data from the capture thread"""
        # This would send audio data to the WebSocket server
        # For now, just log that we received data
        logger.debug(f"Received audio data: {len(audio_data)} bytes")
        
    def update_audio_level(self, level):
        """Update the audio level indicator"""
        level_percent = min(int(level * 100), 100)
        self.audio_level_bar.setValue(level_percent)
        
    def handle_audio_error(self, error_msg):
        """Handle audio capture errors"""
        QMessageBox.critical(self, "Audio Error", f"Audio capture error: {error_msg}")
        self.stop_streaming()
        
    def add_chat_message(self, message, sender="user"):
        """Add a message to the chat area"""
        timestamp = QTimer()
        if sender == "user":
            formatted_msg = f"<div style='color: #2196F3; margin: 5px 0;'><b>You:</b> {message}</div>"
        elif sender == "assistant":
            formatted_msg = f"<div style='color: #4CAF50; margin: 5px 0;'><b>Assistant:</b> {message}</div>"
        else:
            formatted_msg = f"<div style='color: #666; margin: 5px 0; font-style: italic;'>{message}</div>"
        
        self.chat_area.append(formatted_msg)
        
    def hide_to_tray(self):
        """Hide window to system tray"""
        self.hide()
        if hasattr(self, 'tray_icon'):
            self.tray_icon.showMessage(
                "Voice Streaming Assistant",
                "Application was minimized to tray",
                QSystemTrayIcon.MessageIcon.Information,
                2000
            )
            
    def show_from_tray(self):
        """Show window from system tray"""
        self.show()
        self.raise_()
        self.activateWindow()
        
    def tray_icon_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_from_tray()
            
    def restart_server(self):
        """Restart the WebSocket server"""
        if self.websocket_thread and self.websocket_thread.isRunning():
            self.websocket_thread.terminate()
            self.websocket_thread.wait()
            
        self.server_running = False
        self.status_label.setText("🟡 Restarting Server...")
        self.restart_btn.setEnabled(False)
        self.start_btn.setEnabled(False)
        
        # Start server again
        QTimer.singleShot(1000, self.start_websocket_server)
        
    def quit_application(self):
        """Quit the application"""
        if self.is_streaming:
            self.stop_streaming()
            
        if self.websocket_thread and self.websocket_thread.isRunning():
            self.websocket_thread.terminate()
            self.websocket_thread.wait()
        
        # Clean up widget configuration
        cleanup_widget_config()
            
        QApplication.quit()
        
    def closeEvent(self, event):
        """Handle window close event"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.hide_to_tray()
            event.ignore()
        else:
            self.quit_application()

def main():
    """Main function to start the native widget application"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Voice Streaming Assistant - Native Audio")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("ERA-IGNITE")
    
    # Create and show main window
    widget = VoiceStreamingWidgetNative()
    widget.show()
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
