#!/usr/bin/env python3
"""
Test script for the Voice Streaming Widget

This script tests the widget functionality without building the executable.
"""

import sys
import os
import time
import threading

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6.QtWidgets")
    except ImportError as e:
        print(f"❌ PyQt6.QtWidgets: {e}")
        return False
    
    try:
        from PyQt6.QtWebEngineWidgets import QWebEngineView
        print("✅ PyQt6.QtWebEngineWidgets")
    except ImportError as e:
        print(f"❌ PyQt6.QtWebEngineWidgets: {e}")
        return False
    
    try:
        from widget_config import get_widget_config
        print("✅ widget_config")
    except ImportError as e:
        print(f"❌ widget_config: {e}")
        return False
    
    try:
        from voice_streaming_widget import VoiceStreamingWidget
        print("✅ voice_streaming_widget")
    except ImportError as e:
        print(f"❌ voice_streaming_widget: {e}")
        return False
    
    try:
        import websockets
        print("✅ websockets")
    except ImportError as e:
        print(f"❌ websockets: {e}")
        return False
    
    return True

def test_config():
    """Test widget configuration"""
    print("\n⚙️  Testing configuration...")
    
    try:
        from widget_config import get_widget_config
        config = get_widget_config()
        
        print(f"✅ Page title: {config.page_title}")
        print(f"✅ Window size: {config.window_width}x{config.window_height}")
        print(f"✅ WebSocket: {config.websocket_host}:{config.websocket_port}")
        print(f"✅ TTS enabled: {config.enable_tts}")
        print(f"✅ System tray: {config.enable_system_tray}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_websocket_server():
    """Test if WebSocket server can be imported and initialized"""
    print("\n🔌 Testing WebSocket server...")
    
    try:
        from websocket_voice_server import start_voice_server
        print("✅ WebSocket server import successful")
        
        # Test if we can create the server thread
        from voice_streaming_widget import WebSocketServerThread
        thread = WebSocketServerThread(host="localhost", port=8766)  # Use different port for testing
        print("✅ WebSocket server thread creation successful")
        
        return True
    except Exception as e:
        print(f"❌ WebSocket server error: {e}")
        return False

def test_widget_creation():
    """Test widget creation without showing it"""
    print("\n🖥️  Testing widget creation...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from voice_streaming_widget import VoiceStreamingWidget
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create widget (but don't show it)
        widget = VoiceStreamingWidget()
        print("✅ Widget creation successful")
        
        # Test basic properties
        print(f"✅ Window title: {widget.windowTitle()}")
        print(f"✅ Window size: {widget.size().width()}x{widget.size().height()}")
        
        # Clean up
        widget.close()
        
        return True
    except Exception as e:
        print(f"❌ Widget creation error: {e}")
        return False

def test_html_generation():
    """Test HTML content generation"""
    print("\n🌐 Testing HTML generation...")
    
    try:
        from voice_streaming_widget import VoiceStreamingWidget
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        widget = VoiceStreamingWidget()
        html_content = widget.create_widget_html("localhost", 8765)
        
        if html_content and len(html_content) > 100:
            print("✅ HTML content generation successful")
            print(f"✅ HTML length: {len(html_content)} characters")
            
            # Check for key elements
            if "WebSocket" in html_content:
                print("✅ WebSocket integration found")
            if "startStreaming" in html_content:
                print("✅ Streaming functions found")
            if "voice-controls" in html_content:
                print("✅ Voice controls found")
        else:
            print("❌ HTML content too short or empty")
            return False
        
        widget.close()
        return True
    except Exception as e:
        print(f"❌ HTML generation error: {e}")
        return False

def main():
    """Main test function"""
    print("🎙️ Voice Streaming Widget Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_config),
        ("WebSocket Server Test", test_websocket_server),
        ("Widget Creation Test", test_widget_creation),
        ("HTML Generation Test", test_html_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Widget is ready for use.")
        return True
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ Widget testing failed. Please fix the issues before building.")
        sys.exit(1)
    else:
        print("\n✅ Widget testing completed successfully!")
        print("🚀 You can now run the widget with: python widget_launcher.py")
        print("🔨 Or build the executable with: python build_widget.py")
