# Widget-specific requirements for the Voice Streaming Widget
# These are additional dependencies needed for the desktop widget version

# PyQt6 for the desktop GUI (flexible versions for compatibility)
PyQt6>=6.5.0
PyQt6-WebEngine>=6.5.0

# Packaging and executable creation
PyInstaller>=6.0.0
auto-py-to-exe>=2.40.0

# System integration
psutil>=6.0.0

# Core dependencies (subset from main requirements.txt)
websockets>=12.0
python-dotenv>=1.0.0
certifi>=2024.0.0
requests>=2.30.0

# LangChain dependencies
langchain>=0.3.0
langchain_community>=0.3.0
langchain-openai>=0.3.0
langchain-litellm>=0.2.0

# Audio processing
numpy>=1.24.0

# Note: configparser, pathlib, threading, asyncio, json, base64, tempfile, logging
# are built-in Python modules and don't need to be installed
