# Voice Streaming Widget - Self-Contained Desktop Application

A standalone desktop widget that provides voice streaming chatbot functionality without requiring external dependencies, browser setup, or Python installation.

## 🚀 Quick Start

### For End Users (Using Pre-built Executable)

1. **Download** the `VoiceStreamingWidget.exe` file
2. **Double-click** to run the application
3. **Allow microphone access** when prompted
4. **Click "Start Streaming"** and begin speaking
5. **Enjoy continuous voice chat** with AI assistance

### For Developers (Building from Source)

1. **Install Dependencies:**
   ```bash
   pip install -r requirements-widget.txt
   ```

2. **Run the Widget:**
   ```bash
   python widget_launcher.py
   ```

3. **Build Executable:**
   ```bash
   python build_widget.py
   # OR
   build_widget.bat
   ```

## 🎯 Features

- **🎙️ Continuous Voice Streaming** - No button presses needed during conversation
- **🖥️ Native Desktop Application** - Runs without browser or external dependencies
- **🔄 Self-Contained** - Includes WebSocket server and all necessary components
- **📱 System Tray Integration** - Minimize to system tray for background operation
- **⚙️ Configurable** - Customizable settings through configuration files
- **🔊 Text-to-Speech** - AI responses played back as audio
- **💬 Real-time Chat** - Live conversation display with visual feedback
- **🛡️ Feedback Prevention** - AI audio doesn't trigger recording

## 🏗️ Architecture

The widget combines several components into a single application:

```
VoiceStreamingWidget.exe
├── PyQt6 Desktop Interface
├── Embedded WebSocket Server
├── Voice Processing Engine
├── AI Agent System
├── Configuration Manager
└── Bundled Resources
```

### Key Components:

- **`voice_streaming_widget.py`** - Main PyQt6 application
- **`widget_config.py`** - Configuration management for bundled resources
- **`widget_launcher.py`** - Development launcher with dependency checking
- **`build_widget.py`** - PyInstaller build script
- **Embedded WebSocket Server** - Handles voice processing and AI communication
- **QWebEngineView** - Renders voice interface with audio capture

## 📋 System Requirements

### For End Users:
- **Windows 10 or later** (64-bit)
- **Microphone** (built-in or external)
- **Internet connection** (for AI processing)
- **~100MB disk space** for the executable

### For Developers:
- **Python 3.8+**
- **PyQt6** and **PyQt6-WebEngine**
- **All dependencies** from `requirements-widget.txt`

## ⚙️ Configuration

The widget uses a hierarchical configuration system:

1. **Built-in Defaults** - Always available
2. **`config/default.ini`** - Default settings (bundled with executable)
3. **`config/local.ini`** - User overrides (optional)
4. **Environment Variables** - Runtime overrides

### Example Configuration (`config/local.ini`):
```ini
[widget]
window_width = 1200
window_height = 800
websocket_port = 8766
enable_system_tray = true

[tts]
enable_tts = true
autoplay_audio = true

[transcription]
model = gpt-4o-transcribe
language = en
```

## 🔧 Building the Executable

### Automatic Build:
```bash
# Windows
build_widget.bat

# Cross-platform
python build_widget.py
```

### Manual Build:
```bash
# Install PyInstaller
pip install PyInstaller

# Create spec file and build
python build_widget.py
```

### Build Output:
- **`dist/VoiceStreamingWidget.exe`** - Standalone executable
- **`dist/VoiceStreamingWidget_Portable/`** - Portable package with README

## 🎮 Usage Guide

### Starting the Application:
1. **Launch** the executable
2. **Wait** for "Server Running" status (green indicator)
3. **Click "Start Streaming"** to begin voice chat

### Voice Interaction:
1. **Speak naturally** - the system detects speech automatically
2. **Watch audio levels** - visual feedback shows microphone activity
3. **Listen to responses** - AI audio plays automatically
4. **Continue conversation** - no button presses needed

### System Tray:
- **Minimize to tray** - Click "Minimize to Tray" button
- **Show from tray** - Double-click tray icon or right-click → Show
- **Quit from tray** - Right-click tray icon → Quit

## 🐛 Troubleshooting

### Application Won't Start:
- **Run as administrator** if Windows blocks execution
- **Check antivirus settings** - some may flag the executable
- **Verify system requirements** - Windows 10+ required

### Voice Streaming Issues:
- **Check microphone permissions** in Windows Settings
- **Try different microphone** or headset
- **Restart application** if audio stops working
- **Check internet connection** for AI processing

### Server Connection Problems:
- **Wait for green status** before starting streaming
- **Try "Restart Server"** button if connection fails
- **Check firewall settings** if using custom ports

### Performance Issues:
- **Close other audio applications** to avoid conflicts
- **Check system resources** - high CPU/memory usage
- **Reduce window size** if rendering is slow

## 🔒 Security & Privacy

- **Local Processing** - Voice processing happens locally when possible
- **Secure Connections** - All API calls use HTTPS/WSS
- **No Data Storage** - Conversations are not saved locally
- **Configurable APIs** - Use your own API keys for privacy

## 📦 Distribution

### For End Users:
- Distribute the **`VoiceStreamingWidget.exe`** file
- Or provide the **`VoiceStreamingWidget_Portable`** folder
- Include the **README.txt** from the portable package

### For Developers:
- Share the **source code** repository
- Include **build instructions** and dependencies
- Provide **configuration examples**

## 🆘 Support

### Common Issues:
1. **Microphone not detected** → Check Windows audio settings
2. **AI responses slow** → Verify internet connection and API keys
3. **Application crashes** → Run from command line to see error messages
4. **Audio feedback loops** → Use headphones or adjust microphone sensitivity

### Getting Help:
- Check the **troubleshooting section** above
- Review **configuration options** for customization
- Run in **debug mode** for detailed logging
- Consult the **main project documentation**

## 🔄 Updates

To update the widget:
1. **Download new version** of the executable
2. **Replace old executable** with new one
3. **Keep configuration files** (they're compatible)
4. **Restart application** to use new features

## 🎉 Success Indicators

You know everything is working when you see:

**Application Window:**
- ✅ Green "Server Running" status
- ✅ "Connected to server" in voice interface
- ✅ Audio level bars responding to voice

**Voice Interaction:**
- ✅ Real-time transcription appears
- ✅ AI responses display and play automatically
- ✅ Continuous conversation without button presses

**System Integration:**
- ✅ System tray icon appears (if enabled)
- ✅ Minimize/restore functionality works
- ✅ Application starts quickly and reliably
