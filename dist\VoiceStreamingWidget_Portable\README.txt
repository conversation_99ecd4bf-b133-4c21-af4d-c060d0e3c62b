# Voice Streaming Widget - Portable Version

## Quick Start

1. Double-click `VoiceStreamingWidget.exe` to start the application
2. Allow microphone access when prompted
3. Click "Start Streaming" and begin speaking
4. The AI will respond automatically

## Features

- 🎙️ Continuous voice streaming (no button presses needed)
- 🤖 AI-powered responses
- 💬 Real-time chat interface
- 🔊 Text-to-speech audio responses
- 🖥️ System tray integration

## System Requirements

- Windows 10 or later
- Microphone access
- Internet connection (for AI processing)

## Troubleshooting

### If the application won't start:
- Right-click and "Run as administrator"
- Check Windows Defender/antivirus settings
- Ensure microphone permissions are enabled

### If voice streaming doesn't work:
- Check microphone permissions in Windows Settings
- Try a different microphone or headset
- Restart the application

### If AI responses are slow:
- Check your internet connection
- Verify API keys are configured (if using custom models)

## Configuration

The application uses default settings that work out of the box. For advanced
configuration, you can create a `config` folder with custom settings.

## Support

For issues or questions, please refer to the project documentation.

## Version Information

This is a self-contained executable that includes:
- Python runtime
- PyQt6 GUI framework
- WebSocket server
- Voice processing engine
- AI agent system
- All required dependencies

No additional software installation is required.
